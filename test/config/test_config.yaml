# 测试环境配置
app:
  name: "lsenglish_test"
  version: "1.0.0"
  env: "test"

server:
  host: "127.0.0.1"
  port: 8080
  mode: "debug"

database:
  host: "127.0.0.1"
  port: 3306
  username: "root"
  password: "123456"
  database: "lsenglish_test"
  charset: "utf8mb4"
  parseTime: true
  loc: "Local"
  maxIdleConns: 10
  maxOpenConns: 100
  connMaxLifetime: 3600

redis:
  host: "127.0.0.1"
  port: 6379
  password: ""
  db: 1  # 使用不同的数据库避免与生产环境冲突

jwt:
  signKey: "test_secret_key_for_jwt_signing"
  expireSeconds: 86400

log:
  level: "debug"
  format: "json"
  output: "stdout"

# 测试专用配置
test:
  # 数据库配置
  database:
    autoMigrate: true
    cleanupAfterTest: true
    useTransaction: true
  
  # Mock配置
  mock:
    enableExternalAPIMock: true
    enableDatabaseMock: false  # 优先使用真实数据库
  
  # 测试数据配置
  fixtures:
    loadTestData: true
    dataPath: "./test/fixtures"
  
  # 并发测试配置
  concurrency:
    maxGoroutines: 10
    testTimeout: 30  # 秒

# 外部服务配置（测试环境）
external:
  apple:
    bundleId: "com.test.lsenglish"
    environment: "sandbox"
  
  oss:
    endpoint: "test-endpoint"
    accessKeyId: "test-key"
    accessKeySecret: "test-secret"
    bucketName: "test-bucket"
