-- 测试数据库初始化脚本
-- 创建测试数据库
CREATE DATABASE IF NOT EXISTS `lsenglish_test` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `lsenglish_test`;

-- 清理现有数据（测试环境专用）
SET FOREIGN_KEY_CHECKS = 0;

-- 删除所有表（如果存在）
DROP TABLE IF EXISTS `user_benefit_logs`;
DROP TABLE IF EXISTS `vip_benefits`;
DROP TABLE IF EXISTS `user_benefits`;
DROP TABLE IF EXISTS `benefits`;
DROP TABLE IF EXISTS `benefit_groups`;
DROP TABLE IF EXISTS `promotion_codes`;
DROP TABLE IF EXISTS `user_subscription_logs`;
DROP TABLE IF EXISTS `user_subscriptions`;
DROP TABLE IF EXISTS `user_purchase_orders`;
DROP TABLE IF EXISTS `trade_products`;
DROP TABLE IF EXISTS `user_vip_flows`;
DROP TABLE IF EXISTS `user_vip_relations`;
DROP TABLE IF EXISTS `vips`;
DROP TABLE IF EXISTS `learning_plan_days`;
DROP TABLE IF EXISTS `learning_plan_weeks`;
DROP TABLE IF EXISTS `learning_plan_stages`;
DROP TABLE IF EXISTS `learning_plans`;
DROP TABLE IF EXISTS `user_questionnaires`;
DROP TABLE IF EXISTS `user_feedbacks`;
DROP TABLE IF EXISTS `speech_evaluations`;
DROP TABLE IF EXISTS `user_remote_resource_relations`;
DROP TABLE IF EXISTS `user_local_resources`;
DROP TABLE IF EXISTS `user_subtitle_relations`;
DROP TABLE IF EXISTS `note_collect_relations`;
DROP TABLE IF EXISTS `notes`;
DROP TABLE IF EXISTS `watch_histories`;
DROP TABLE IF EXISTS `user_player_configs`;
DROP TABLE IF EXISTS `deleted_users`;
DROP TABLE IF EXISTS `users`;
DROP TABLE IF EXISTS `data_episode_eaches`;
DROP TABLE IF EXISTS `data_episodes`;
DROP TABLE IF EXISTS `featured_contents`;
DROP TABLE IF EXISTS `series_resource_relations`;
DROP TABLE IF EXISTS `category_resource_relations`;
DROP TABLE IF EXISTS `category_series_relations`;
DROP TABLE IF EXISTS `resource_relations`;
DROP TABLE IF EXISTS `resources`;
DROP TABLE IF EXISTS `series_relations`;
DROP TABLE IF EXISTS `series`;
DROP TABLE IF EXISTS `categories`;
DROP TABLE IF EXISTS `category_types`;
DROP TABLE IF EXISTS `sys_roles`;
DROP TABLE IF EXISTS `sys_users`;

SET FOREIGN_KEY_CHECKS = 1;

-- 插入测试基础数据

-- 系统用户
INSERT INTO `sys_users` (`id`, `username`, `password_digest`, `status`, `created_at`, `updated_at`) VALUES
(1, 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXgwrekO0fUiAEuYVQw5u2WG/Gy', 0, NOW(), NOW());

-- 系统角色
INSERT INTO `sys_roles` (`id`, `name`, `description`, `created_at`, `updated_at`) VALUES
(1, 'admin', '管理员', NOW(), NOW());

-- VIP等级
INSERT INTO `vips` (`id`, `name`, `level`, `created_at`, `updated_at`) VALUES
(1, '普通会员', 1, NOW(), NOW()),
(2, 'Pro会员', 100, NOW(), NOW()),
(3, 'Ultra会员', 1000, NOW(), NOW());

-- 权益组
INSERT INTO `benefit_groups` (`id`, `group_code`, `group_name`, `description`, `status`, `create_time`, `create_timestamp`) VALUES
(1, 'VIDEO_LIMIT', '视频观看限制', '控制用户每日可观看的视频数量', 1, NOW(), UNIX_TIMESTAMP(NOW()) * 1000),
(2, 'AI_CALL_LIMIT', 'AI调用限制', '控制用户每日可调用AI的次数', 1, NOW(), UNIX_TIMESTAMP(NOW()) * 1000),
(3, 'PREMIUM_CONTENT', '高级内容访问', '访问高级内容的权限', 1, NOW(), UNIX_TIMESTAMP(NOW()) * 1000);

-- 权益
INSERT INTO `benefits` (`id`, `benefit_group_id`, `benefit_code`, `benefit_name`, `description`, `benefit_type`, `default_value`, `create_time`, `create_timestamp`) VALUES
(1, 1, 'DAILY_VIDEO_LIMIT', '每日视频观看限制', '用户每日可观看的视频数量', 1, 5, NOW(), UNIX_TIMESTAMP(NOW()) * 1000),
(2, 2, 'DAILY_AI_CALL_LIMIT', '每日AI调用限制', '用户每日可调用AI的次数', 1, 10, NOW(), UNIX_TIMESTAMP(NOW()) * 1000),
(3, 3, 'PREMIUM_ACCESS', '高级内容访问权限', '是否可以访问高级内容', 2, 0, NOW(), UNIX_TIMESTAMP(NOW()) * 1000);

-- VIP权益关联
INSERT INTO `vip_benefits` (`id`, `vip_id`, `vip_level`, `benefit_group_id`, `benefit_id`, `benefit_code`, `create_time`, `create_timestamp`) VALUES
(1, 1, 1, 1, 1, 'DAILY_VIDEO_LIMIT', NOW(), UNIX_TIMESTAMP(NOW()) * 1000),
(2, 1, 1, 2, 2, 'DAILY_AI_CALL_LIMIT', NOW(), UNIX_TIMESTAMP(NOW()) * 1000),
(3, 2, 100, 1, 1, 'DAILY_VIDEO_LIMIT', NOW(), UNIX_TIMESTAMP(NOW()) * 1000),
(4, 2, 100, 2, 2, 'DAILY_AI_CALL_LIMIT', NOW(), UNIX_TIMESTAMP(NOW()) * 1000),
(5, 2, 100, 3, 3, 'PREMIUM_ACCESS', NOW(), UNIX_TIMESTAMP(NOW()) * 1000),
(6, 3, 1000, 1, 1, 'DAILY_VIDEO_LIMIT', NOW(), UNIX_TIMESTAMP(NOW()) * 1000),
(7, 3, 1000, 2, 2, 'DAILY_AI_CALL_LIMIT', NOW(), UNIX_TIMESTAMP(NOW()) * 1000),
(8, 3, 1000, 3, 3, 'PREMIUM_ACCESS', NOW(), UNIX_TIMESTAMP(NOW()) * 1000);

-- 商品
INSERT INTO `trade_products` (`id`, `name`, `type`, `is_subscription`, `price`, `origin_price`, `ios_product_id`, `currency`, `terminal`, `days`, `vip_level`, `created_at`, `updated_at`) VALUES
(1, 'Pro月度订阅', 1, 1, 9.99, 9.99, 'com.lsenglish.pro.monthly', 'USD', 2, 30, 100, NOW(), NOW()),
(2, 'Pro年度订阅', 1, 1, 99.99, 119.88, 'com.lsenglish.pro.yearly', 'USD', 2, 365, 100, NOW(), NOW()),
(3, 'Ultra月度订阅', 1, 1, 19.99, 19.99, 'com.lsenglish.ultra.monthly', 'USD', 2, 30, 1000, NOW(), NOW());

-- 分类类型
INSERT INTO `category_types` (`id`, `name`, `description`, `created_at`, `updated_at`) VALUES
(1, '难度等级', '按学习难度分类', NOW(), NOW()),
(2, '内容主题', '按内容主题分类', NOW(), NOW());

-- 分类
INSERT INTO `categories` (`id`, `name`, `description`, `category_type_id`, `created_at`, `updated_at`) VALUES
(1, '初级', '适合初学者', 1, NOW(), NOW()),
(2, '中级', '适合有一定基础的学习者', 1, NOW(), NOW()),
(3, '高级', '适合高级学习者', 1, NOW(), NOW()),
(4, '商务英语', '商务场景英语', 2, NOW(), NOW()),
(5, '日常对话', '日常生活对话', 2, NOW(), NOW());

-- 系列
INSERT INTO `series` (`id`, `name`, `description`, `cover_url`, `created_at`, `updated_at`) VALUES
(1, '基础英语对话', '适合初学者的基础英语对话系列', 'https://example.com/cover1.jpg', NOW(), NOW()),
(2, '商务英语精讲', '商务场景下的英语应用', 'https://example.com/cover2.jpg', NOW(), NOW());

-- 资源
INSERT INTO `resources` (`id`, `title`, `description`, `video_url`, `subtitle_url`, `duration`, `level`, `created_at`, `updated_at`) VALUES
(1, '自我介绍', '学习如何用英语进行自我介绍', 'https://example.com/video1.mp4', 'https://example.com/subtitle1.srt', 300, 'beginner', NOW(), NOW()),
(2, '商务会议', '商务会议中的常用英语表达', 'https://example.com/video2.mp4', 'https://example.com/subtitle2.srt', 600, 'intermediate', NOW(), NOW());
