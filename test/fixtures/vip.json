{"vips": [{"id": 1001, "name": "测试普通会员", "level": 1}, {"id": 1002, "name": "测试Pro会员", "level": 100}, {"id": 1003, "name": "测试Ultra会员", "level": 1000}], "user_vip_relations": [{"uid": "1001", "is_vip": 0, "vip_id": 1001, "is_subscription": 0, "expire_date": "2024-01-01", "expire_timestamp": 1704067200000}, {"uid": "1002", "is_vip": 1, "vip_id": 1002, "is_subscription": 1, "expire_date": "2024-12-31", "expire_timestamp": 1735603200000}, {"uid": "1003", "is_vip": 1, "vip_id": 1003, "is_subscription": 0, "expire_date": "2024-06-30", "expire_timestamp": 1719705600000}], "trade_products": [{"id": 1001, "name": "Pro月度订阅测试", "type": 1, "is_subscription": 1, "price": 9.99, "origin_price": 9.99, "ios_product_id": "com.test.lsenglish.pro.monthly", "currency": "USD", "terminal": 2, "days": 30, "vip_level": 100}, {"id": 1002, "name": "Pro年度订阅测试", "type": 1, "is_subscription": 1, "price": 99.99, "origin_price": 119.88, "ios_product_id": "com.test.lsenglish.pro.yearly", "currency": "USD", "terminal": 2, "days": 365, "vip_level": 100}, {"id": 1003, "name": "Ultra月度订阅测试", "type": 1, "is_subscription": 1, "price": 19.99, "origin_price": 19.99, "ios_product_id": "com.test.lsenglish.ultra.monthly", "currency": "USD", "terminal": 2, "days": 30, "vip_level": 1000}, {"id": 1004, "name": "Android Pro月度测试", "type": 1, "is_subscription": 1, "price": 9.99, "origin_price": 9.99, "ios_product_id": "com.test.lsenglish.android.pro.monthly", "currency": "USD", "terminal": 1, "days": 30, "vip_level": 100}], "promotion_codes": [{"id": 1001, "code": "TEST_PRO_30", "days": 30, "vip_level": 100, "status": 0, "uid": "", "created_timestamp": 1703001600000, "redeemed_timestamp": 0}, {"id": 1002, "code": "TEST_ULTRA_7", "days": 7, "vip_level": 1000, "status": 0, "uid": "", "created_timestamp": 1703001600000, "redeemed_timestamp": 0}, {"id": 1003, "code": "USED_CODE_123", "days": 30, "vip_level": 100, "status": 1, "uid": "1001", "created_timestamp": 1703001600000, "redeemed_timestamp": 1703088000000}], "user_vip_flows": [{"uid": "1002", "vip_id": 1002, "vip_level": 100, "days": 30, "source": 1, "source_detail": "苹果内购", "start_timestamp": 1703001600000, "end_timestamp": 1705593600000}, {"uid": "1003", "vip_id": 1003, "vip_level": 1000, "days": 7, "source": 2, "source_detail": "兑换码", "start_timestamp": 1703001600000, "end_timestamp": 1703606400000}], "user_subscriptions": [{"uid": "1002", "product_id": 1001, "product_name": "Pro月度订阅测试", "payment_provider": 1, "out_transaction_id": "test_transaction_123", "apple_original_transaction_id": "test_original_123", "first_cycle_amount": 9.99, "cycle_amount": 9.99, "currency": "USD", "status": 1, "start_timestamp": 1703001600000, "end_timestamp": 1705593600000, "auto_renew": true, "trial_period": false, "trial_end_timestamp": 0}], "user_purchase_orders": [{"uid": "1002", "product_id": 1001, "subscription_id": 1, "order_no": "TEST_ORDER_001", "amount": 9.99, "currency": "USD", "payment_provider": 1, "out_transaction_id": "test_transaction_123", "apple_original_transaction_id": "test_original_123", "status": 2, "created_timestamp": 1703001600000, "paid_timestamp": 1703001660000}, {"uid": "1001", "product_id": 1001, "subscription_id": 0, "order_no": "TEST_ORDER_002", "amount": 9.99, "currency": "USD", "payment_provider": 1, "out_transaction_id": "test_transaction_456", "apple_original_transaction_id": "test_original_456", "status": 1, "created_timestamp": 1703002600000, "paid_timestamp": 0}]}