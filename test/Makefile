# LSEnglish Backend Test Makefile
# 用于简化测试执行和管理

.PHONY: help test test-unit test-integration test-coverage test-clean test-setup test-db test-deps
.DEFAULT_GOAL := help

# 项目配置
PROJECT_NAME := lsenglish_backend
TEST_DIR := $(CURDIR)
PROJECT_ROOT := $(shell dirname $(TEST_DIR))
COVERAGE_DIR := $(TEST_DIR)/coverage
REPORTS_DIR := $(TEST_DIR)/reports

# Go配置
GO := go
GO_TEST := $(GO) test
GO_BUILD := $(GO) build
GO_MOD := $(GO) mod

# 测试配置
TEST_TIMEOUT := 30m
TEST_PARALLEL := 4
COVERAGE_MODE := atomic
COVERAGE_THRESHOLD := 80

# 数据库配置
TEST_DB_HOST := localhost
TEST_DB_PORT := 3306
TEST_DB_USER := root
TEST_DB_PASSWORD := 123456
TEST_DB_NAME := lsenglish_test
TEST_DB_DSN := $(TEST_DB_USER):$(TEST_DB_PASSWORD)@tcp($(TEST_DB_HOST):$(TEST_DB_PORT))/$(TEST_DB_NAME)?charset=utf8mb4&parseTime=True&loc=Local

# Redis配置
TEST_REDIS_ADDR := localhost:6379
TEST_REDIS_DB := 1

# 颜色定义
RED := \033[0;31m
GREEN := \033[0;32m
YELLOW := \033[1;33m
BLUE := \033[0;34m
NC := \033[0m # No Color

help: ## 显示帮助信息
	@echo "$(BLUE)LSEnglish Backend Test Makefile$(NC)"
	@echo ""
	@echo "$(GREEN)Available targets:$(NC)"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  $(YELLOW)%-20s$(NC) %s\n", $$1, $$2}' $(MAKEFILE_LIST)
	@echo ""
	@echo "$(GREEN)Environment Variables:$(NC)"
	@echo "  TEST_DB_DSN      Test database connection string"
	@echo "  TEST_REDIS_ADDR  Test Redis server address"
	@echo "  TEST_TIMEOUT     Test timeout duration (default: $(TEST_TIMEOUT))"
	@echo "  TEST_PARALLEL    Number of parallel test processes (default: $(TEST_PARALLEL))"

setup: ## 设置测试环境
	@echo "$(BLUE)Setting up test environment...$(NC)"
	@mkdir -p $(COVERAGE_DIR)
	@mkdir -p $(REPORTS_DIR)
	@mkdir -p $(TEST_DIR)/logs
	@mkdir -p $(TEST_DIR)/uploads
	@mkdir -p $(TEST_DIR)/storage
	@echo "$(GREEN)Test environment setup complete$(NC)"

clean: ## 清理测试文件和报告
	@echo "$(BLUE)Cleaning test files...$(NC)"
	@rm -rf $(COVERAGE_DIR)
	@rm -rf $(REPORTS_DIR)
	@rm -rf $(TEST_DIR)/logs
	@rm -rf $(TEST_DIR)/uploads
	@rm -rf $(TEST_DIR)/storage
	@echo "$(GREEN)Test files cleaned$(NC)"

deps: ## 安装测试依赖
	@echo "$(BLUE)Installing test dependencies...$(NC)"
	@cd $(PROJECT_ROOT) && $(GO_MOD) tidy
	@cd $(PROJECT_ROOT) && $(GO_MOD) download
	@echo "$(GREEN)Test dependencies installed$(NC)"

test-db: ## 设置测试数据库
	@echo "$(BLUE)Setting up test database...$(NC)"
	@mysql -h$(TEST_DB_HOST) -P$(TEST_DB_PORT) -u$(TEST_DB_USER) -p$(TEST_DB_PASSWORD) -e "CREATE DATABASE IF NOT EXISTS $(TEST_DB_NAME) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" || true
	@mysql -h$(TEST_DB_HOST) -P$(TEST_DB_PORT) -u$(TEST_DB_USER) -p$(TEST_DB_PASSWORD) $(TEST_DB_NAME) < $(TEST_DIR)/config/database.sql || true
	@echo "$(GREEN)Test database setup complete$(NC)"

test: setup ## 运行所有测试
	@echo "$(BLUE)Running all tests...$(NC)"
	@export TEST_DB_DSN="$(TEST_DB_DSN)" && \
	export TEST_REDIS_ADDR="$(TEST_REDIS_ADDR)" && \
	cd $(PROJECT_ROOT) && \
	$(GO_TEST) -v -race -timeout=$(TEST_TIMEOUT) -parallel=$(TEST_PARALLEL) \
		-coverprofile=$(COVERAGE_DIR)/coverage.out \
		-covermode=$(COVERAGE_MODE) \
		./test/... 2>&1 | tee $(REPORTS_DIR)/test.log
	@$(MAKE) coverage-report
	@echo "$(GREEN)All tests completed$(NC)"

test-unit: setup ## 运行单元测试
	@echo "$(BLUE)Running unit tests...$(NC)"
	@export TEST_DB_DSN="$(TEST_DB_DSN)" && \
	export TEST_REDIS_ADDR="$(TEST_REDIS_ADDR)" && \
	cd $(PROJECT_ROOT) && \
	$(GO_TEST) -v -race -timeout=$(TEST_TIMEOUT) -parallel=$(TEST_PARALLEL) \
		-coverprofile=$(COVERAGE_DIR)/unit_coverage.out \
		-covermode=$(COVERAGE_MODE) \
		-short ./test/... 2>&1 | tee $(REPORTS_DIR)/unit_test.log
	@echo "$(GREEN)Unit tests completed$(NC)"

test-integration: setup ## 运行集成测试
	@echo "$(BLUE)Running integration tests...$(NC)"
	@export TEST_DB_DSN="$(TEST_DB_DSN)" && \
	export TEST_REDIS_ADDR="$(TEST_REDIS_ADDR)" && \
	cd $(PROJECT_ROOT) && \
	$(GO_TEST) -v -race -timeout=$(TEST_TIMEOUT) -parallel=1 \
		-coverprofile=$(COVERAGE_DIR)/integration_coverage.out \
		-covermode=$(COVERAGE_MODE) \
		-run="Integration" ./test/... 2>&1 | tee $(REPORTS_DIR)/integration_test.log
	@echo "$(GREEN)Integration tests completed$(NC)"

test-user: setup ## 运行用户模块测试
	@echo "$(BLUE)Running user module tests...$(NC)"
	@export TEST_DB_DSN="$(TEST_DB_DSN)" && \
	export TEST_REDIS_ADDR="$(TEST_REDIS_ADDR)" && \
	cd $(PROJECT_ROOT) && \
	$(GO_TEST) -v -race -timeout=$(TEST_TIMEOUT) \
		-coverprofile=$(COVERAGE_DIR)/user_coverage.out \
		-covermode=$(COVERAGE_MODE) \
		./test/user/... 2>&1 | tee $(REPORTS_DIR)/user_test.log
	@echo "$(GREEN)User module tests completed$(NC)"

test-vip: setup ## 运行VIP模块测试
	@echo "$(BLUE)Running VIP module tests...$(NC)"
	@export TEST_DB_DSN="$(TEST_DB_DSN)" && \
	export TEST_REDIS_ADDR="$(TEST_REDIS_ADDR)" && \
	cd $(PROJECT_ROOT) && \
	$(GO_TEST) -v -race -timeout=$(TEST_TIMEOUT) \
		-coverprofile=$(COVERAGE_DIR)/vip_coverage.out \
		-covermode=$(COVERAGE_MODE) \
		./test/vip/... 2>&1 | tee $(REPORTS_DIR)/vip_test.log
	@echo "$(GREEN)VIP module tests completed$(NC)"

test-benefit: setup ## 运行权益模块测试
	@echo "$(BLUE)Running benefit module tests...$(NC)"
	@export TEST_DB_DSN="$(TEST_DB_DSN)" && \
	export TEST_REDIS_ADDR="$(TEST_REDIS_ADDR)" && \
	cd $(PROJECT_ROOT) && \
	$(GO_TEST) -v -race -timeout=$(TEST_TIMEOUT) \
		-coverprofile=$(COVERAGE_DIR)/benefit_coverage.out \
		-covermode=$(COVERAGE_MODE) \
		./test/benefit/... 2>&1 | tee $(REPORTS_DIR)/benefit_test.log
	@echo "$(GREEN)Benefit module tests completed$(NC)"

test-plan: setup ## 运行计划模块测试
	@echo "$(BLUE)Running plan module tests...$(NC)"
	@export TEST_DB_DSN="$(TEST_DB_DSN)" && \
	export TEST_REDIS_ADDR="$(TEST_REDIS_ADDR)" && \
	cd $(PROJECT_ROOT) && \
	$(GO_TEST) -v -race -timeout=$(TEST_TIMEOUT) \
		-coverprofile=$(COVERAGE_DIR)/plan_coverage.out \
		-covermode=$(COVERAGE_MODE) \
		./test/plan/... 2>&1 | tee $(REPORTS_DIR)/plan_test.log
	@echo "$(GREEN)Plan module tests completed$(NC)"

test-player: setup ## 运行播放器模块测试
	@echo "$(BLUE)Running player module tests...$(NC)"
	@export TEST_DB_DSN="$(TEST_DB_DSN)" && \
	export TEST_REDIS_ADDR="$(TEST_REDIS_ADDR)" && \
	cd $(PROJECT_ROOT) && \
	$(GO_TEST) -v -race -timeout=$(TEST_TIMEOUT) \
		-coverprofile=$(COVERAGE_DIR)/player_coverage.out \
		-covermode=$(COVERAGE_MODE) \
		./test/player/... 2>&1 | tee $(REPORTS_DIR)/player_test.log
	@echo "$(GREEN)Player module tests completed$(NC)"

test-video: setup ## 运行视频模块测试
	@echo "$(BLUE)Running video module tests...$(NC)"
	@export TEST_DB_DSN="$(TEST_DB_DSN)" && \
	export TEST_REDIS_ADDR="$(TEST_REDIS_ADDR)" && \
	cd $(PROJECT_ROOT) && \
	$(GO_TEST) -v -race -timeout=$(TEST_TIMEOUT) \
		-coverprofile=$(COVERAGE_DIR)/video_coverage.out \
		-covermode=$(COVERAGE_MODE) \
		./test/video/... 2>&1 | tee $(REPORTS_DIR)/video_test.log
	@echo "$(GREEN)Video module tests completed$(NC)"

coverage: ## 生成覆盖率报告
	@echo "$(BLUE)Generating coverage report...$(NC)"
	@if [ -f $(COVERAGE_DIR)/coverage.out ]; then \
		$(GO) tool cover -html=$(COVERAGE_DIR)/coverage.out -o $(REPORTS_DIR)/coverage.html; \
		$(GO) tool cover -func=$(COVERAGE_DIR)/coverage.out | tail -1; \
		echo "$(GREEN)Coverage report generated: $(REPORTS_DIR)/coverage.html$(NC)"; \
	else \
		echo "$(RED)No coverage file found. Run tests first.$(NC)"; \
	fi

coverage-report: ## 生成详细覆盖率报告
	@echo "$(BLUE)Generating detailed coverage report...$(NC)"
	@if [ -f $(COVERAGE_DIR)/coverage.out ]; then \
		$(GO) tool cover -html=$(COVERAGE_DIR)/coverage.out -o $(REPORTS_DIR)/coverage.html; \
		$(GO) tool cover -func=$(COVERAGE_DIR)/coverage.out > $(REPORTS_DIR)/coverage.txt; \
		COVERAGE=$$($(GO) tool cover -func=$(COVERAGE_DIR)/coverage.out | grep "total:" | awk '{print $$3}' | sed 's/%//'); \
		echo "Total Coverage: $$COVERAGE%"; \
		if [ $$(echo "$$COVERAGE < $(COVERAGE_THRESHOLD)" | bc -l) -eq 1 ]; then \
			echo "$(RED)Coverage $$COVERAGE% is below threshold $(COVERAGE_THRESHOLD)%$(NC)"; \
			exit 1; \
		else \
			echo "$(GREEN)Coverage $$COVERAGE% meets threshold $(COVERAGE_THRESHOLD)%$(NC)"; \
		fi; \
	else \
		echo "$(RED)No coverage file found. Run tests first.$(NC)"; \
	fi

benchmark: setup ## 运行基准测试
	@echo "$(BLUE)Running benchmark tests...$(NC)"
	@export TEST_DB_DSN="$(TEST_DB_DSN)" && \
	export TEST_REDIS_ADDR="$(TEST_REDIS_ADDR)" && \
	cd $(PROJECT_ROOT) && \
	$(GO_TEST) -bench=. -benchmem -timeout=$(TEST_TIMEOUT) \
		./test/... 2>&1 | tee $(REPORTS_DIR)/benchmark.log
	@echo "$(GREEN)Benchmark tests completed$(NC)"

race: setup ## 运行竞态条件检测
	@echo "$(BLUE)Running race condition detection...$(NC)"
	@export TEST_DB_DSN="$(TEST_DB_DSN)" && \
	export TEST_REDIS_ADDR="$(TEST_REDIS_ADDR)" && \
	cd $(PROJECT_ROOT) && \
	$(GO_TEST) -race -timeout=$(TEST_TIMEOUT) \
		./test/... 2>&1 | tee $(REPORTS_DIR)/race.log
	@echo "$(GREEN)Race condition detection completed$(NC)"

lint: ## 运行代码检查
	@echo "$(BLUE)Running code linting...$(NC)"
	@cd $(PROJECT_ROOT) && \
	golangci-lint run ./test/... 2>&1 | tee $(REPORTS_DIR)/lint.log || true
	@echo "$(GREEN)Code linting completed$(NC)"

fmt: ## 格式化测试代码
	@echo "$(BLUE)Formatting test code...$(NC)"
	@cd $(PROJECT_ROOT) && \
	$(GO) fmt ./test/...
	@echo "$(GREEN)Test code formatted$(NC)"

vet: ## 运行go vet检查
	@echo "$(BLUE)Running go vet...$(NC)"
	@cd $(PROJECT_ROOT) && \
	$(GO) vet ./test/... 2>&1 | tee $(REPORTS_DIR)/vet.log
	@echo "$(GREEN)Go vet completed$(NC)"

mod-tidy: ## 整理Go模块依赖
	@echo "$(BLUE)Tidying Go modules...$(NC)"
	@cd $(PROJECT_ROOT) && \
	$(GO_MOD) tidy
	@echo "$(GREEN)Go modules tidied$(NC)"

check: fmt vet lint ## 运行所有代码检查

ci: deps test-db test coverage-report ## CI环境测试流程
	@echo "$(GREEN)CI test pipeline completed$(NC)"

dev: setup test-user test-vip ## 开发环境快速测试
	@echo "$(GREEN)Development tests completed$(NC)"

full: clean setup deps test-db test coverage-report benchmark ## 完整测试流程
	@echo "$(GREEN)Full test suite completed$(NC)"

status: ## 显示测试环境状态
	@echo "$(BLUE)Test Environment Status$(NC)"
	@echo "Project Root: $(PROJECT_ROOT)"
	@echo "Test Directory: $(TEST_DIR)"
	@echo "Coverage Directory: $(COVERAGE_DIR)"
	@echo "Reports Directory: $(REPORTS_DIR)"
	@echo "Database DSN: $(TEST_DB_DSN)"
	@echo "Redis Address: $(TEST_REDIS_ADDR)"
	@echo ""
	@echo "$(GREEN)Go Environment:$(NC)"
	@$(GO) version
	@echo ""
	@echo "$(GREEN)Available Test Modules:$(NC)"
	@ls -la $(TEST_DIR) | grep "^d" | grep -v "^\." | awk '{print "  " $$9}' | grep -E "(user|vip|benefit|plan|player|video)"
