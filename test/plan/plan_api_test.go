package plan

import (
	"bytes"
	"encoding/json"
	"loop/internal/model"
	"loop/test/utils"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// PlanApiTestSuite 计划API测试套件
type PlanApiTestSuite struct {
	suite.Suite
	helper       *utils.TestHelper
	dbHelper     *utils.DBHelper
	assertHelper *utils.AssertHelper
	router       *gin.Engine
}

// SetupSuite 设置测试套件
func (suite *PlanApiTestSuite) SetupSuite() {
	suite.helper = utils.NewTestHelper(suite.T())
	suite.dbHelper = utils.NewDBHelper(suite.helper.DB)
	suite.assertHelper = utils.NewAssertHelper(suite.T())

	// 设置路由
	suite.router = gin.New()
	suite.setupRoutes()
}

// setupRoutes 设置测试路由
func (suite *PlanApiTestSuite) setupRoutes() {
	v1 := suite.router.Group("/api/v1")

	// 需要认证的路由
	protected := v1.Group("/")
	protected.Use(suite.mockJWTMiddleware())
	{
		protected.GET("/plan", suite.mockGetPlan)
		protected.POST("/plan/questionnaire", suite.mockSubmitQuestionnaire)
		protected.POST("/plan/generate", suite.mockGeneratePlan)
		protected.PUT("/plan/progress", suite.mockUpdateProgress)
		protected.GET("/plan/progress", suite.mockGetProgress)
		protected.POST("/plan/feedback", suite.mockSubmitFeedback)
	}
}

// mockJWTMiddleware 模拟JWT中间件
func (suite *PlanApiTestSuite) mockJWTMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.GetHeader("X-Test-UID")
		if uid != "" {
			c.Set("uid", uid)
			c.Set("username", "testuser")
			c.Set("status", 0)
		}
		c.Next()
	}
}

// 模拟API处理函数
func (suite *PlanApiTestSuite) mockGetPlan(c *gin.Context) {
	uid := c.GetString("uid")
	if uid == "" {
		c.JSON(401, gin.H{"code": 401, "msg": "未授权"})
		return
	}

	// 查找用户的活跃计划
	var plan model.LearningPlan
	err := suite.helper.DB.First(&plan, "uid = ? AND status = ?", uid, 1).Error
	if err != nil {
		c.JSON(200, gin.H{"code": 0, "msg": "success", "data": nil})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success", "data": plan})
}

func (suite *PlanApiTestSuite) mockSubmitQuestionnaire(c *gin.Context) {
	uid := c.GetString("uid")
	if uid == "" {
		c.JSON(401, gin.H{"code": 401, "msg": "未授权"})
		return
	}

	var req struct {
		CurrentLevel      string `json:"current_level"`
		TargetLevel       string `json:"target_level"`
		DailyStudyMinutes int    `json:"daily_study_minutes"`
		MotivationSource  string `json:"motivation_source"`
		DesiredAbility    string `json:"desired_ability"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"code": 400, "msg": "参数错误"})
		return
	}

	questionnaire := &model.UserQuestionnaire{
		Uid:                uid,
		CurrentLevel:       req.CurrentLevel,
		TargetLevel:        req.TargetLevel,
		DailyStudyMinutes:  req.DailyStudyMinutes,
		MotivationSource:   req.MotivationSource,
		DesiredAbility:     req.DesiredAbility,
	}

	err := suite.helper.DB.Create(questionnaire).Error
	if err != nil {
		c.JSON(500, gin.H{"code": 500, "msg": "保存失败"})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success"})
}

func (suite *PlanApiTestSuite) mockGeneratePlan(c *gin.Context) {
	uid := c.GetString("uid")
	if uid == "" {
		c.JSON(401, gin.H{"code": 401, "msg": "未授权"})
		return
	}

	var req struct {
		StartLevel  string `json:"start_level"`
		TargetLevel string `json:"target_level"`
		Days        int    `json:"days"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"code": 400, "msg": "参数错误"})
		return
	}

	plan := &model.LearningPlan{
		Uid:             uid,
		StartLevel:      req.StartLevel,
		TargetLevel:     req.TargetLevel,
		Status:          1, // 进行中
		TotalLearnDays:  req.Days,
		DailySentences:  10,
	}

	err := suite.helper.DB.Create(plan).Error
	if err != nil {
		c.JSON(500, gin.H{"code": 500, "msg": "创建失败"})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success", "data": plan})
}

func (suite *PlanApiTestSuite) mockUpdateProgress(c *gin.Context) {
	c.JSON(200, gin.H{"code": 0, "msg": "success"})
}

func (suite *PlanApiTestSuite) mockGetProgress(c *gin.Context) {
	c.JSON(200, gin.H{"code": 0, "msg": "success", "data": gin.H{
		"total_days":          30,
		"completed_days":      5,
		"progress_percentage": 16.67,
	}})
}

func (suite *PlanApiTestSuite) mockSubmitFeedback(c *gin.Context) {
	c.JSON(200, gin.H{"code": 0, "msg": "success"})
}

// TearDownSuite 清理测试套件
func (suite *PlanApiTestSuite) TearDownSuite() {
	suite.helper.CleanupDB(suite.T())
}

// SetupTest 每个测试前的设置
func (suite *PlanApiTestSuite) SetupTest() {
	// 清理测试数据
	tables := []string{
		"learning_plan_days", "learning_plan_weeks", "learning_plan_stages",
		"learning_plans", "user_questionnaires", "user_feedbacks",
	}
	suite.dbHelper.CleanupTestData(suite.T(), tables...)
}

// TestSubmitQuestionnaireAPI 测试提交问卷API
func (suite *PlanApiTestSuite) TestSubmitQuestionnaireAPI() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "planapiuser001")

	reqData := map[string]interface{}{
		"current_level":       "beginner",
		"target_level":        "intermediate",
		"daily_study_minutes": 30,
		"motivation_source":   "career",
		"desired_ability":     "speaking",
	}

	jsonData, _ := json.Marshal(reqData)
	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("POST", "/api/v1/plan/questionnaire", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证问卷已保存
	var questionnaire model.UserQuestionnaire
	err := suite.helper.DB.First(&questionnaire, "uid = ?", testUser.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), "beginner", questionnaire.CurrentLevel)
	assert.Equal(suite.T(), "intermediate", questionnaire.TargetLevel)
}

// TestGeneratePlanAPI 测试生成计划API
func (suite *PlanApiTestSuite) TestGeneratePlanAPI() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "planapiuser002")

	reqData := map[string]interface{}{
		"start_level":  "beginner",
		"target_level": "intermediate",
		"days":         30,
	}

	jsonData, _ := json.Marshal(reqData)
	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("POST", "/api/v1/plan/generate", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证计划已创建
	var plan model.LearningPlan
	err := suite.helper.DB.First(&plan, "uid = ?", testUser.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), "beginner", plan.StartLevel)
	assert.Equal(suite.T(), "intermediate", plan.TargetLevel)
	assert.Equal(suite.T(), 30, plan.TotalLearnDays)
}

// TestGetPlanAPI 测试获取计划API
func (suite *PlanApiTestSuite) TestGetPlanAPI() {
	// 创建测试用户和计划
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "planapiuser003")
	plan := suite.dbHelper.CreateTestLearningPlan(suite.T(), testUser.Id, "beginner", "intermediate", 30)

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/plan", nil)
	req.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, req)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证返回的计划数据
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)

	data, ok := response["data"].(map[string]interface{})
	require.True(suite.T(), ok)
	assert.Equal(suite.T(), plan.StartLevel, data["start_level"])
	assert.Equal(suite.T(), plan.TargetLevel, data["target_level"])
}

// TestGetProgressAPI 测试获取进度API
func (suite *PlanApiTestSuite) TestGetProgressAPI() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "planapiuser004")

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/plan/progress", nil)
	req.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, req)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证返回的进度数据
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)

	data, ok := response["data"].(map[string]interface{})
	require.True(suite.T(), ok)
	assert.Contains(suite.T(), data, "total_days")
	assert.Contains(suite.T(), data, "completed_days")
	assert.Contains(suite.T(), data, "progress_percentage")
}

// TestUnauthorizedAccess 测试未授权访问
func (suite *PlanApiTestSuite) TestUnauthorizedAccess() {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/plan", nil)
	// 不设置X-Test-UID头部

	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), 401, w.Code)
}

// 运行测试套件
func TestPlanApiSuite(t *testing.T) {
	suite.Run(t, new(PlanApiTestSuite))
}
