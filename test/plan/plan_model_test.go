package plan

import (
	"loop/internal/model"
	"loop/pkg/timex"
	"loop/test/utils"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// PlanModelTestSuite 计划模型测试套件
type PlanModelTestSuite struct {
	suite.Suite
	helper    *utils.TestHelper
	dbHelper  *utils.DBHelper
	planModel *model.PlanModel
	ctx       *gin.Context
}

// SetupSuite 设置测试套件
func (suite *PlanModelTestSuite) SetupSuite() {
	suite.helper = utils.NewTestHelper(suite.T())
	suite.dbHelper = utils.NewDBHelper(suite.helper.DB)

	// 创建测试上下文
	suite.ctx, _ = gin.CreateTestContext(nil)

	// 创建计划模型实例
	dbModel := &model.DbModel{DBExtension: *suite.helper.DB}
	suite.planModel = model.NewPlanModel(dbModel, suite.helper.Config)
}

// TearDownSuite 清理测试套件
func (suite *PlanModelTestSuite) TearDownSuite() {
	suite.helper.CleanupDB(suite.T())
}

// SetupTest 每个测试前的设置
func (suite *PlanModelTestSuite) SetupTest() {
	// 清理测试数据
	tables := []string{
		"learning_plan_days", "learning_plan_weeks", "learning_plan_stages",
		"learning_plans", "user_questionnaires", "user_feedbacks",
	}
	suite.dbHelper.CleanupTestData(suite.T(), tables...)
}

// TestCreateLearningPlan 测试创建学习计划
func (suite *PlanModelTestSuite) TestCreateLearningPlan() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "planuser001")
	plan := suite.dbHelper.CreateTestLearningPlan(suite.T(), testUser.Id, "beginner", "intermediate", 30)

	assert.NotEmpty(suite.T(), plan.Id)
	assert.Equal(suite.T(), testUser.Id, plan.Uid)
	assert.Equal(suite.T(), "beginner", plan.StartLevel)
	assert.Equal(suite.T(), "intermediate", plan.TargetLevel)
	assert.Equal(suite.T(), 1, plan.Status) // 进行中
	assert.Equal(suite.T(), 30, plan.TotalLearnDays)
}

// TestGetUserActivePlan 测试获取用户活跃计划
func (suite *PlanModelTestSuite) TestGetUserActivePlan() {
	// 创建测试用户和计划
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "planuser002")
	plan := suite.dbHelper.CreateTestLearningPlan(suite.T(), testUser.Id, "beginner", "intermediate", 30)

	// 获取用户活跃计划
	var activePlan model.LearningPlan
	found, err := suite.planModel.GetOne(&activePlan, model.LearningPlan{Uid: testUser.Id, Status: 1})
	require.NoError(suite.T(), err)
	require.True(suite.T(), found)
	assert.Equal(suite.T(), plan.Id, activePlan.Id)
	assert.Equal(suite.T(), plan.Uid, activePlan.Uid)
	assert.Equal(suite.T(), 1, activePlan.Status)

	// 测试获取不存在的活跃计划
	var notFoundPlan model.LearningPlan
	found, err = suite.planModel.GetOne(&notFoundPlan, model.LearningPlan{Uid: "nonexistent", Status: 1})
	require.NoError(suite.T(), err)
	assert.False(suite.T(), found)
}

// TestUpdatePlanStatus 测试更新计划状态
func (suite *PlanModelTestSuite) TestUpdatePlanStatus() {
	// 创建测试计划
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "planuser003")
	plan := suite.dbHelper.CreateTestLearningPlan(suite.T(), testUser.Id, "beginner", "intermediate", 30)

	// 更新计划状态为已完成
	plan.Status = 2 // 已完成
	plan.CompletedTimestamp = timex.Now().UnixMilli()
	err := suite.planModel.Update(plan, "id = ?", plan.Id)
	require.NoError(suite.T(), err)

	// 验证更新结果
	var updatedPlan model.LearningPlan
	err = suite.helper.DB.First(&updatedPlan, "id = ?", plan.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 2, updatedPlan.Status)
	assert.NotZero(suite.T(), updatedPlan.CompletedTimestamp)
}

// TestCreateLearningPlanStage 测试创建学习计划阶段
func (suite *PlanModelTestSuite) TestCreateLearningPlanStage() {
	// 创建测试计划
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "planuser004")
	plan := suite.dbHelper.CreateTestLearningPlan(suite.T(), testUser.Id, "beginner", "intermediate", 30)

	// 创建计划阶段
	stage := &model.LearningPlanStage{
		PlanID:      plan.Id,
		StageNumber: 1,
		StageName:   "基础阶段",
		Description: "学习基础英语对话",
		Days:        10,
		ResourceIDs: []string{"resource_001", "resource_002"},
		LsCounts:    []int{5, 8},
		Status:      0, // 未开始
	}

	err := suite.planModel.SaveOne(stage)
	require.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), stage.Id)

	// 验证阶段已保存
	var savedStage model.LearningPlanStage
	err = suite.helper.DB.First(&savedStage, "id = ?", stage.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), plan.Id, savedStage.PlanID)
	assert.Equal(suite.T(), 1, savedStage.StageNumber)
	assert.Equal(suite.T(), "基础阶段", savedStage.StageName)
	assert.Equal(suite.T(), 10, savedStage.Days)
}

// TestGetPlanStages 测试获取计划阶段
func (suite *PlanModelTestSuite) TestGetPlanStages() {
	// 创建测试计划
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "planuser005")
	plan := suite.dbHelper.CreateTestLearningPlan(suite.T(), testUser.Id, "beginner", "intermediate", 30)

	// 创建多个阶段
	stages := []*model.LearningPlanStage{
		{
			PlanID:      plan.Id,
			StageNumber: 1,
			StageName:   "基础阶段",
			Description: "学习基础英语对话",
			Days:        10,
			ResourceIDs: []string{"resource_001", "resource_002"},
			LsCounts:    []int{5, 8},
			Status:      1, // 进行中
		},
		{
			PlanID:      plan.Id,
			StageNumber: 2,
			StageName:   "进阶阶段",
			Description: "学习进阶英语对话",
			Days:        20,
			ResourceIDs: []string{"resource_003", "resource_004"},
			LsCounts:    []int{10, 12},
			Status:      0, // 未开始
		},
	}

	for _, stage := range stages {
		err := suite.planModel.SaveOne(stage)
		require.NoError(suite.T(), err)
	}

	// 获取计划阶段
	var planStages []model.LearningPlanStage
	err := suite.planModel.GetOrderedList(&planStages, "stage_number ASC", model.LearningPlanStage{PlanID: plan.Id})
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), planStages, 2)

	// 验证按阶段号排序
	assert.Equal(suite.T(), 1, planStages[0].StageNumber)
	assert.Equal(suite.T(), 2, planStages[1].StageNumber)
	assert.Equal(suite.T(), "基础阶段", planStages[0].StageName)
	assert.Equal(suite.T(), "进阶阶段", planStages[1].StageName)
}

// TestCreateLearningPlanWeek 测试创建学习计划周
func (suite *PlanModelTestSuite) TestCreateLearningPlanWeek() {
	// 创建测试计划和阶段
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "planuser006")
	plan := suite.dbHelper.CreateTestLearningPlan(suite.T(), testUser.Id, "beginner", "intermediate", 30)

	stage := &model.LearningPlanStage{
		PlanID:      plan.Id,
		StageNumber: 1,
		StageName:   "基础阶段",
		Days:        10,
		Status:      1,
	}
	err := suite.planModel.SaveOne(stage)
	require.NoError(suite.T(), err)

	// 创建计划周
	week := &model.LearningPlanWeek{
		PlanID:      plan.Id,
		StageID:     stage.Id,
		WeekNumber:  1,
		WeekName:    "第一周",
		Description: "基础词汇学习",
		Days:        7,
		Status:      0, // 未开始
	}

	err = suite.planModel.SaveOne(week)
	require.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), week.Id)

	// 验证周已保存
	var savedWeek model.LearningPlanWeek
	err = suite.helper.DB.First(&savedWeek, "id = ?", week.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), plan.Id, savedWeek.PlanID)
	assert.Equal(suite.T(), stage.Id, savedWeek.StageID)
	assert.Equal(suite.T(), 1, savedWeek.WeekNumber)
	assert.Equal(suite.T(), "第一周", savedWeek.WeekName)
}

// TestCreateLearningPlanDay 测试创建学习计划日
func (suite *PlanModelTestSuite) TestCreateLearningPlanDay() {
	// 创建测试计划、阶段和周
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "planuser007")
	plan := suite.dbHelper.CreateTestLearningPlan(suite.T(), testUser.Id, "beginner", "intermediate", 30)

	stage := &model.LearningPlanStage{
		PlanID:      plan.Id,
		StageNumber: 1,
		StageName:   "基础阶段",
		Days:        10,
		Status:      1,
	}
	err := suite.planModel.SaveOne(stage)
	require.NoError(suite.T(), err)

	week := &model.LearningPlanWeek{
		PlanID:     plan.Id,
		StageID:    stage.Id,
		WeekNumber: 1,
		WeekName:   "第一周",
		Days:       7,
		Status:     1,
	}
	err = suite.planModel.SaveOne(week)
	require.NoError(suite.T(), err)

	// 创建计划日
	day := &model.LearningPlanDay{
		PlanID:      plan.Id,
		StageID:     stage.Id,
		WeekID:      week.Id,
		DayNumber:   1,
		DayName:     "第一天",
		Description: "学习基础问候语",
		ResourceID:  "resource_001",
		LsCount:     5,
		Status:      0, // 未开始
	}

	err = suite.planModel.SaveOne(day)
	require.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), day.Id)

	// 验证日已保存
	var savedDay model.LearningPlanDay
	err = suite.helper.DB.First(&savedDay, "id = ?", day.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), plan.Id, savedDay.PlanID)
	assert.Equal(suite.T(), stage.Id, savedDay.StageID)
	assert.Equal(suite.T(), week.Id, savedDay.WeekID)
	assert.Equal(suite.T(), 1, savedDay.DayNumber)
	assert.Equal(suite.T(), "resource_001", savedDay.ResourceID)
	assert.Equal(suite.T(), 5, savedDay.LsCount)
}

// TestGetPlanDays 测试获取计划日
func (suite *PlanModelTestSuite) TestGetPlanDays() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "planuser008")
	plan := suite.dbHelper.CreateTestLearningPlan(suite.T(), testUser.Id, "beginner", "intermediate", 30)

	stage := &model.LearningPlanStage{
		PlanID:      plan.Id,
		StageNumber: 1,
		StageName:   "基础阶段",
		Days:        10,
		Status:      1,
	}
	err := suite.planModel.SaveOne(stage)
	require.NoError(suite.T(), err)

	week := &model.LearningPlanWeek{
		PlanID:     plan.Id,
		StageID:    stage.Id,
		WeekNumber: 1,
		WeekName:   "第一周",
		Days:       7,
		Status:     1,
	}
	err = suite.planModel.SaveOne(week)
	require.NoError(suite.T(), err)

	// 创建多个计划日
	days := []*model.LearningPlanDay{
		{
			PlanID:      plan.Id,
			StageID:     stage.Id,
			WeekID:      week.Id,
			DayNumber:   1,
			DayName:     "第一天",
			Description: "学习基础问候语",
			ResourceID:  "resource_001",
			LsCount:     5,
			Status:      2, // 已完成
		},
		{
			PlanID:      plan.Id,
			StageID:     stage.Id,
			WeekID:      week.Id,
			DayNumber:   2,
			DayName:     "第二天",
			Description: "学习自我介绍",
			ResourceID:  "resource_002",
			LsCount:     8,
			Status:      1, // 进行中
		},
	}

	for _, day := range days {
		err := suite.planModel.SaveOne(day)
		require.NoError(suite.T(), err)
	}

	// 获取计划日
	var planDays []model.LearningPlanDay
	err = suite.planModel.GetOrderedList(&planDays, "day_number ASC", model.LearningPlanDay{PlanID: plan.Id})
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), planDays, 2)

	// 验证按日号排序
	assert.Equal(suite.T(), 1, planDays[0].DayNumber)
	assert.Equal(suite.T(), 2, planDays[1].DayNumber)
	assert.Equal(suite.T(), "第一天", planDays[0].DayName)
	assert.Equal(suite.T(), "第二天", planDays[1].DayName)
}

// TestUpdateDayStatus 测试更新日状态
func (suite *PlanModelTestSuite) TestUpdateDayStatus() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "planuser009")
	plan := suite.dbHelper.CreateTestLearningPlan(suite.T(), testUser.Id, "beginner", "intermediate", 30)

	stage := &model.LearningPlanStage{
		PlanID:      plan.Id,
		StageNumber: 1,
		StageName:   "基础阶段",
		Days:        10,
		Status:      1,
	}
	err := suite.planModel.SaveOne(stage)
	require.NoError(suite.T(), err)

	week := &model.LearningPlanWeek{
		PlanID:     plan.Id,
		StageID:    stage.Id,
		WeekNumber: 1,
		WeekName:   "第一周",
		Days:       7,
		Status:     1,
	}
	err = suite.planModel.SaveOne(week)
	require.NoError(suite.T(), err)

	day := &model.LearningPlanDay{
		PlanID:     plan.Id,
		StageID:    stage.Id,
		WeekID:     week.Id,
		DayNumber:  1,
		DayName:    "第一天",
		ResourceID: "resource_001",
		LsCount:    5,
		Status:     1, // 进行中
	}
	err = suite.planModel.SaveOne(day)
	require.NoError(suite.T(), err)

	// 更新日状态为已完成
	day.Status = 2 // 已完成
	day.CompletedTimestamp = timex.Now().UnixMilli()
	err = suite.planModel.Update(day, "id = ?", day.Id)
	require.NoError(suite.T(), err)

	// 验证更新结果
	var updatedDay model.LearningPlanDay
	err = suite.helper.DB.First(&updatedDay, "id = ?", day.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 2, updatedDay.Status)
	assert.NotZero(suite.T(), updatedDay.CompletedTimestamp)
}

// TestCreateUserQuestionnaire 测试创建用户问卷
func (suite *PlanModelTestSuite) TestCreateUserQuestionnaire() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "planuser010")

	questionnaire := &model.UserQuestionnaire{
		Uid:                testUser.Id,
		CurrentLevel:       "beginner",
		TargetLevel:        "intermediate",
		DailyStudyMinutes:  30,
		MotivationSource:   "career",
		DesiredAbility:     "speaking",
	}

	err := suite.planModel.SaveOne(questionnaire)
	require.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), questionnaire.Id)

	// 验证问卷已保存
	var savedQuestionnaire model.UserQuestionnaire
	err = suite.helper.DB.First(&savedQuestionnaire, "uid = ?", testUser.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), testUser.Id, savedQuestionnaire.Uid)
	assert.Equal(suite.T(), "beginner", savedQuestionnaire.CurrentLevel)
	assert.Equal(suite.T(), "intermediate", savedQuestionnaire.TargetLevel)
	assert.Equal(suite.T(), 30, savedQuestionnaire.DailyStudyMinutes)
}

// TestGetUserQuestionnaire 测试获取用户问卷
func (suite *PlanModelTestSuite) TestGetUserQuestionnaire() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "planuser011")

	// 创建问卷
	questionnaire := &model.UserQuestionnaire{
		Uid:                testUser.Id,
		CurrentLevel:       "intermediate",
		TargetLevel:        "advanced",
		DailyStudyMinutes:  60,
		MotivationSource:   "interest",
		DesiredAbility:     "listening",
	}
	err := suite.planModel.SaveOne(questionnaire)
	require.NoError(suite.T(), err)

	// 获取用户问卷
	var foundQuestionnaire model.UserQuestionnaire
	found, err := suite.planModel.GetOne(&foundQuestionnaire, model.UserQuestionnaire{Uid: testUser.Id})
	require.NoError(suite.T(), err)
	require.True(suite.T(), found)
	assert.Equal(suite.T(), questionnaire.Id, foundQuestionnaire.Id)
	assert.Equal(suite.T(), "intermediate", foundQuestionnaire.CurrentLevel)
	assert.Equal(suite.T(), "advanced", foundQuestionnaire.TargetLevel)

	// 测试获取不存在的问卷
	var notFoundQuestionnaire model.UserQuestionnaire
	found, err = suite.planModel.GetOne(&notFoundQuestionnaire, model.UserQuestionnaire{Uid: "nonexistent"})
	require.NoError(suite.T(), err)
	assert.False(suite.T(), found)
}

// 运行测试套件
func TestPlanModelSuite(t *testing.T) {
	suite.Run(t, new(PlanModelTestSuite))
}
