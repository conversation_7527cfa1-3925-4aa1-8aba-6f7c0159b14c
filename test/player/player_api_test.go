package player

import (
	"bytes"
	"encoding/json"
	"loop/internal/model"
	"loop/test/utils"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// PlayerApiTestSuite 播放器API测试套件
type PlayerApiTestSuite struct {
	suite.Suite
	helper       *utils.TestHelper
	dbHelper     *utils.DBHelper
	assertHelper *utils.AssertHelper
	router       *gin.Engine
}

// SetupSuite 设置测试套件
func (suite *PlayerApiTestSuite) SetupSuite() {
	suite.helper = utils.NewTestHelper(suite.T())
	suite.dbHelper = utils.NewDBHelper(suite.helper.DB)
	suite.assertHelper = utils.NewAssertHelper(suite.T())

	// 设置路由
	suite.router = gin.New()
	suite.setupRoutes()
}

// setupRoutes 设置测试路由
func (suite *PlayerApiTestSuite) setupRoutes() {
	v1 := suite.router.Group("/api/v1")

	// 需要认证的路由
	protected := v1.Group("/")
	protected.Use(suite.mockJWTMiddleware())
	{
		// 笔记相关
		protected.POST("/notes", suite.mockCreateNote)
		protected.GET("/notes", suite.mockGetNotes)
		protected.PUT("/notes/:id", suite.mockUpdateNote)
		protected.DELETE("/notes/:id", suite.mockDeleteNote)
		protected.POST("/notes/:id/collect", suite.mockCollectNote)
		protected.DELETE("/notes/:id/collect", suite.mockUncollectNote)
		protected.GET("/notes/collected", suite.mockGetCollectedNotes)

		// 语音评估相关
		protected.POST("/speech/evaluate", suite.mockCreateSpeechEvaluation)
		protected.GET("/speech/evaluations", suite.mockGetSpeechEvaluations)

		// 字幕相关
		protected.POST("/subtitles", suite.mockCreateSubtitleRelation)
		protected.GET("/subtitles/:resourceId", suite.mockGetSubtitles)
	}
}

// mockJWTMiddleware 模拟JWT中间件
func (suite *PlayerApiTestSuite) mockJWTMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.GetHeader("X-Test-UID")
		if uid != "" {
			c.Set("uid", uid)
			c.Set("username", "testuser")
			c.Set("status", 0)
		}
		c.Next()
	}
}

// 模拟API处理函数
func (suite *PlayerApiTestSuite) mockCreateNote(c *gin.Context) {
	uid := c.GetString("uid")
	if uid == "" {
		c.JSON(401, gin.H{"code": 401, "msg": "未授权"})
		return
	}

	var req struct {
		ResourceId string `json:"resource_id"`
		Position   int    `json:"position"`
		Content    string `json:"content"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"code": 400, "msg": "参数错误"})
		return
	}

	note := &model.Note{
		Uid:        uid,
		ResourceId: req.ResourceId,
		Position:   req.Position,
		Content:    req.Content,
		Timestamp:  time.Now().UnixMilli(),
	}

	err := suite.helper.DB.Create(note).Error
	if err != nil {
		c.JSON(500, gin.H{"code": 500, "msg": "创建失败"})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success", "data": note})
}

func (suite *PlayerApiTestSuite) mockGetNotes(c *gin.Context) {
	uid := c.GetString("uid")
	resourceId := c.Query("resource_id")

	var notes []model.Note
	query := suite.helper.DB.Where("uid = ?", uid)
	if resourceId != "" {
		query = query.Where("resource_id = ?", resourceId)
	}
	
	err := query.Order("position ASC").Find(&notes).Error
	if err != nil {
		c.JSON(500, gin.H{"code": 500, "msg": "查询失败"})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success", "data": notes})
}

func (suite *PlayerApiTestSuite) mockUpdateNote(c *gin.Context) {
	uid := c.GetString("uid")
	noteId := c.Param("id")

	var req struct {
		Content  string `json:"content"`
		Position int    `json:"position"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"code": 400, "msg": "参数错误"})
		return
	}

	err := suite.helper.DB.Model(&model.Note{}).
		Where("id = ? AND uid = ?", noteId, uid).
		Updates(map[string]interface{}{
			"content":  req.Content,
			"position": req.Position,
		}).Error

	if err != nil {
		c.JSON(500, gin.H{"code": 500, "msg": "更新失败"})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success"})
}

func (suite *PlayerApiTestSuite) mockDeleteNote(c *gin.Context) {
	uid := c.GetString("uid")
	noteId := c.Param("id")

	err := suite.helper.DB.Where("id = ? AND uid = ?", noteId, uid).Delete(&model.Note{}).Error
	if err != nil {
		c.JSON(500, gin.H{"code": 500, "msg": "删除失败"})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success"})
}

func (suite *PlayerApiTestSuite) mockCollectNote(c *gin.Context) {
	uid := c.GetString("uid")
	noteId := c.Param("id")

	relation := &model.NoteCollectRelation{
		Uid:       uid,
		NoteId:    noteId,
		Timestamp: time.Now().UnixMilli(),
	}

	err := suite.helper.DB.Create(relation).Error
	if err != nil {
		c.JSON(500, gin.H{"code": 500, "msg": "收藏失败"})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success"})
}

func (suite *PlayerApiTestSuite) mockUncollectNote(c *gin.Context) {
	uid := c.GetString("uid")
	noteId := c.Param("id")

	err := suite.helper.DB.Where("uid = ? AND note_id = ?", uid, noteId).Delete(&model.NoteCollectRelation{}).Error
	if err != nil {
		c.JSON(500, gin.H{"code": 500, "msg": "取消收藏失败"})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success"})
}

func (suite *PlayerApiTestSuite) mockGetCollectedNotes(c *gin.Context) {
	uid := c.GetString("uid")

	var relations []model.NoteCollectRelation
	err := suite.helper.DB.Where("uid = ?", uid).Order("timestamp DESC").Find(&relations).Error
	if err != nil {
		c.JSON(500, gin.H{"code": 500, "msg": "查询失败"})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success", "data": relations})
}

func (suite *PlayerApiTestSuite) mockCreateSpeechEvaluation(c *gin.Context) {
	uid := c.GetString("uid")

	var req struct {
		ResourceId string  `json:"resource_id"`
		Position   int     `json:"position"`
		Text       string  `json:"text"`
		AudioUrl   string  `json:"audio_url"`
		Score      float64 `json:"score"`
		Feedback   string  `json:"feedback"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"code": 400, "msg": "参数错误"})
		return
	}

	evaluation := &model.SpeechEvaluation{
		Uid:        uid,
		ResourceId: req.ResourceId,
		Position:   req.Position,
		Text:       req.Text,
		AudioUrl:   req.AudioUrl,
		Score:      req.Score,
		Feedback:   req.Feedback,
		Timestamp:  time.Now().UnixMilli(),
	}

	err := suite.helper.DB.Create(evaluation).Error
	if err != nil {
		c.JSON(500, gin.H{"code": 500, "msg": "创建失败"})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success", "data": evaluation})
}

func (suite *PlayerApiTestSuite) mockGetSpeechEvaluations(c *gin.Context) {
	uid := c.GetString("uid")
	resourceId := c.Query("resource_id")

	var evaluations []model.SpeechEvaluation
	query := suite.helper.DB.Where("uid = ?", uid)
	if resourceId != "" {
		query = query.Where("resource_id = ?", resourceId)
	}

	err := query.Order("timestamp DESC").Find(&evaluations).Error
	if err != nil {
		c.JSON(500, gin.H{"code": 500, "msg": "查询失败"})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success", "data": evaluations})
}

func (suite *PlayerApiTestSuite) mockCreateSubtitleRelation(c *gin.Context) {
	uid := c.GetString("uid")

	var req struct {
		ResourceId   string `json:"resource_id"`
		SubtitleType string `json:"subtitle_type"`
		SubtitleUrl  string `json:"subtitle_url"`
		Language     string `json:"language"`
		IsDefault    bool   `json:"is_default"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"code": 400, "msg": "参数错误"})
		return
	}

	relation := &model.UserSubtitleRelation{
		Uid:          uid,
		ResourceId:   req.ResourceId,
		SubtitleType: req.SubtitleType,
		SubtitleUrl:  req.SubtitleUrl,
		Language:     req.Language,
		IsDefault:    req.IsDefault,
		Timestamp:    time.Now().UnixMilli(),
	}

	err := suite.helper.DB.Create(relation).Error
	if err != nil {
		c.JSON(500, gin.H{"code": 500, "msg": "创建失败"})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success", "data": relation})
}

func (suite *PlayerApiTestSuite) mockGetSubtitles(c *gin.Context) {
	uid := c.GetString("uid")
	resourceId := c.Param("resourceId")

	var relations []model.UserSubtitleRelation
	err := suite.helper.DB.Where("uid = ? AND resource_id = ?", uid, resourceId).Find(&relations).Error
	if err != nil {
		c.JSON(500, gin.H{"code": 500, "msg": "查询失败"})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success", "data": relations})
}

// TearDownSuite 清理测试套件
func (suite *PlayerApiTestSuite) TearDownSuite() {
	suite.helper.CleanupDB(suite.T())
}

// SetupTest 每个测试前的设置
func (suite *PlayerApiTestSuite) SetupTest() {
	// 清理测试数据
	tables := []string{
		"speech_evaluations", "note_collect_relations", "notes",
		"user_subtitle_relations", "user_remote_resource_relations",
		"user_local_resources", "watch_histories", "user_player_configs",
	}
	suite.dbHelper.CleanupTestData(suite.T(), tables...)
}

// TestCreateNoteAPI 测试创建笔记API
func (suite *PlayerApiTestSuite) TestCreateNoteAPI() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "playerapiuser001")

	reqData := map[string]interface{}{
		"resource_id": "resource_001",
		"position":    120,
		"content":     "这是一个测试笔记",
	}

	jsonData, _ := json.Marshal(reqData)
	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("POST", "/api/v1/notes", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证笔记已创建
	var note model.Note
	err := suite.helper.DB.First(&note, "uid = ? AND resource_id = ?", testUser.Id, "resource_001").Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), "这是一个测试笔记", note.Content)
	assert.Equal(suite.T(), 120, note.Position)
}

// TestGetNotesAPI 测试获取笔记API
func (suite *PlayerApiTestSuite) TestGetNotesAPI() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "playerapiuser002")

	// 创建测试笔记
	notes := []*model.Note{
		{
			Uid:        testUser.Id,
			ResourceId: "resource_001",
			Position:   60,
			Content:    "第一个笔记",
			Timestamp:  time.Now().UnixMilli(),
		},
		{
			Uid:        testUser.Id,
			ResourceId: "resource_001",
			Position:   120,
			Content:    "第二个笔记",
			Timestamp:  time.Now().UnixMilli() + 1000,
		},
	}

	for _, note := range notes {
		err := suite.helper.DB.Create(note).Error
		require.NoError(suite.T(), err)
	}

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/notes?resource_id=resource_001", nil)
	req.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, req)

	suite.assertHelper.AssertSuccessResponse(w, nil)
	suite.assertHelper.AssertListResponse(w, 2)
}

// TestCreateSpeechEvaluationAPI 测试创建语音评估API
func (suite *PlayerApiTestSuite) TestCreateSpeechEvaluationAPI() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "playerapiuser003")

	reqData := map[string]interface{}{
		"resource_id": "resource_001",
		"position":    120,
		"text":        "Hello world",
		"audio_url":   "https://test.com/audio.mp3",
		"score":       85.5,
		"feedback":    "发音不错",
	}

	jsonData, _ := json.Marshal(reqData)
	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("POST", "/api/v1/speech/evaluate", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证语音评估已创建
	var evaluation model.SpeechEvaluation
	err := suite.helper.DB.First(&evaluation, "uid = ? AND resource_id = ?", testUser.Id, "resource_001").Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), "Hello world", evaluation.Text)
	assert.Equal(suite.T(), 85.5, evaluation.Score)
}

// TestCollectNoteAPI 测试收藏笔记API
func (suite *PlayerApiTestSuite) TestCollectNoteAPI() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "playerapiuser004")

	// 先创建笔记
	note := &model.Note{
		Uid:        testUser.Id,
		ResourceId: "resource_001",
		Position:   120,
		Content:    "要收藏的笔记",
		Timestamp:  time.Now().UnixMilli(),
	}
	err := suite.helper.DB.Create(note).Error
	require.NoError(suite.T(), err)

	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/notes/"+note.Id+"/collect", nil)
	req.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, req)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证收藏关系已创建
	var relation model.NoteCollectRelation
	err = suite.helper.DB.First(&relation, "uid = ? AND note_id = ?", testUser.Id, note.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), testUser.Id, relation.Uid)
	assert.Equal(suite.T(), note.Id, relation.NoteId)
}

// TestUnauthorizedAccess 测试未授权访问
func (suite *PlayerApiTestSuite) TestUnauthorizedAccess() {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/notes", nil)
	// 不设置X-Test-UID头部

	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), 401, w.Code)
}

// 运行测试套件
func TestPlayerApiSuite(t *testing.T) {
	suite.Run(t, new(PlayerApiTestSuite))
}
