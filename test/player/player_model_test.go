package player

import (
	"loop/internal/model"
	"loop/test/utils"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// PlayerModelTestSuite 播放器模型测试套件
type PlayerModelTestSuite struct {
	suite.Suite
	helper      *utils.TestHelper
	dbHelper    *utils.DBHelper
	playerModel *model.PlayerModel
	ctx         *gin.Context
}

// SetupSuite 设置测试套件
func (suite *PlayerModelTestSuite) SetupSuite() {
	suite.helper = utils.NewTestHelper(suite.T())
	suite.dbHelper = utils.NewDBHelper(suite.helper.DB)

	// 创建测试上下文
	suite.ctx, _ = gin.CreateTestContext(nil)

	// 创建播放器模型实例
	dbModel := &model.DbModel{DBExtension: *suite.helper.DB}
	suite.playerModel = model.NewPlayerModel(dbModel, suite.helper.Config)
}

// TearDownSuite 清理测试套件
func (suite *PlayerModelTestSuite) TearDownSuite() {
	suite.helper.CleanupDB(suite.T())
}

// SetupTest 每个测试前的设置
func (suite *PlayerModelTestSuite) SetupTest() {
	// 清理测试数据
	tables := []string{
		"speech_evaluations", "note_collect_relations", "notes",
		"user_subtitle_relations", "user_remote_resource_relations",
		"user_local_resources", "watch_histories", "user_player_configs",
	}
	suite.dbHelper.CleanupTestData(suite.T(), tables...)
}

// TestCreateNote 测试创建笔记
func (suite *PlayerModelTestSuite) TestCreateNote() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "playeruser001")

	note := &model.Note{
		Uid:        testUser.Id,
		ResourceId: "resource_001",
		Position:   120, // 2分钟位置
		Content:    "这是一个测试笔记",
		Timestamp:  time.Now().UnixMilli(),
	}

	err := suite.playerModel.SaveOne(note)
	require.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), note.Id)

	// 验证笔记已保存
	var savedNote model.Note
	err = suite.helper.DB.First(&savedNote, "id = ?", note.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), testUser.Id, savedNote.Uid)
	assert.Equal(suite.T(), "resource_001", savedNote.ResourceId)
	assert.Equal(suite.T(), 120, savedNote.Position)
	assert.Equal(suite.T(), "这是一个测试笔记", savedNote.Content)
}

// TestGetUserNotes 测试获取用户笔记
func (suite *PlayerModelTestSuite) TestGetUserNotes() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "playeruser002")

	// 创建多个笔记
	notes := []*model.Note{
		{
			Uid:        testUser.Id,
			ResourceId: "resource_001",
			Position:   60,
			Content:    "第一个笔记",
			Timestamp:  time.Now().UnixMilli(),
		},
		{
			Uid:        testUser.Id,
			ResourceId: "resource_001",
			Position:   180,
			Content:    "第二个笔记",
			Timestamp:  time.Now().UnixMilli() + 1000,
		},
		{
			Uid:        testUser.Id,
			ResourceId: "resource_002",
			Position:   90,
			Content:    "第三个笔记",
			Timestamp:  time.Now().UnixMilli() + 2000,
		},
	}

	for _, note := range notes {
		err := suite.playerModel.SaveOne(note)
		require.NoError(suite.T(), err)
	}

	// 获取特定资源的笔记
	var resourceNotes []model.Note
	err := suite.playerModel.GetOrderedList(&resourceNotes, "position ASC", 
		model.Note{Uid: testUser.Id, ResourceId: "resource_001"})
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), resourceNotes, 2)

	// 验证按位置排序
	assert.Equal(suite.T(), 60, resourceNotes[0].Position)
	assert.Equal(suite.T(), 180, resourceNotes[1].Position)
	assert.Equal(suite.T(), "第一个笔记", resourceNotes[0].Content)
	assert.Equal(suite.T(), "第二个笔记", resourceNotes[1].Content)
}

// TestUpdateNote 测试更新笔记
func (suite *PlayerModelTestSuite) TestUpdateNote() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "playeruser003")

	// 创建笔记
	note := &model.Note{
		Uid:        testUser.Id,
		ResourceId: "resource_001",
		Position:   120,
		Content:    "原始笔记内容",
		Timestamp:  time.Now().UnixMilli(),
	}
	err := suite.playerModel.SaveOne(note)
	require.NoError(suite.T(), err)

	// 更新笔记内容
	note.Content = "更新后的笔记内容"
	note.Position = 150
	err = suite.playerModel.Update(note, "id = ?", note.Id)
	require.NoError(suite.T(), err)

	// 验证更新结果
	var updatedNote model.Note
	err = suite.helper.DB.First(&updatedNote, "id = ?", note.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), "更新后的笔记内容", updatedNote.Content)
	assert.Equal(suite.T(), 150, updatedNote.Position)
}

// TestDeleteNote 测试删除笔记
func (suite *PlayerModelTestSuite) TestDeleteNote() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "playeruser004")

	// 创建笔记
	note := &model.Note{
		Uid:        testUser.Id,
		ResourceId: "resource_001",
		Position:   120,
		Content:    "要删除的笔记",
		Timestamp:  time.Now().UnixMilli(),
	}
	err := suite.playerModel.SaveOne(note)
	require.NoError(suite.T(), err)

	// 删除笔记
	err = suite.playerModel.Delete(note, "id = ?", note.Id)
	require.NoError(suite.T(), err)

	// 验证笔记已删除
	var count int64
	suite.helper.DB.Model(&model.Note{}).Where("id = ?", note.Id).Count(&count)
	assert.Equal(suite.T(), int64(0), count)
}

// TestCreateNoteCollectRelation 测试创建笔记收藏关系
func (suite *PlayerModelTestSuite) TestCreateNoteCollectRelation() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "playeruser005")

	// 先创建笔记
	note := &model.Note{
		Uid:        testUser.Id,
		ResourceId: "resource_001",
		Position:   120,
		Content:    "收藏的笔记",
		Timestamp:  time.Now().UnixMilli(),
	}
	err := suite.playerModel.SaveOne(note)
	require.NoError(suite.T(), err)

	// 创建收藏关系
	relation := &model.NoteCollectRelation{
		Uid:       testUser.Id,
		NoteId:    note.Id,
		Timestamp: time.Now().UnixMilli(),
	}

	err = suite.playerModel.SaveOne(relation)
	require.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), relation.Id)

	// 验证收藏关系已创建
	var savedRelation model.NoteCollectRelation
	err = suite.helper.DB.First(&savedRelation, "uid = ? AND note_id = ?", testUser.Id, note.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), testUser.Id, savedRelation.Uid)
	assert.Equal(suite.T(), note.Id, savedRelation.NoteId)
}

// TestGetCollectedNotes 测试获取收藏的笔记
func (suite *PlayerModelTestSuite) TestGetCollectedNotes() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "playeruser006")

	// 创建笔记和收藏关系
	notes := []*model.Note{
		{
			Uid:        testUser.Id,
			ResourceId: "resource_001",
			Position:   60,
			Content:    "收藏笔记1",
			Timestamp:  time.Now().UnixMilli(),
		},
		{
			Uid:        testUser.Id,
			ResourceId: "resource_002",
			Position:   120,
			Content:    "收藏笔记2",
			Timestamp:  time.Now().UnixMilli() + 1000,
		},
	}

	for _, note := range notes {
		err := suite.playerModel.SaveOne(note)
		require.NoError(suite.T(), err)

		// 创建收藏关系
		relation := &model.NoteCollectRelation{
			Uid:       testUser.Id,
			NoteId:    note.Id,
			Timestamp: time.Now().UnixMilli(),
		}
		err = suite.playerModel.SaveOne(relation)
		require.NoError(suite.T(), err)
	}

	// 获取收藏的笔记
	var relations []model.NoteCollectRelation
	err := suite.playerModel.GetOrderedList(&relations, "timestamp DESC", 
		model.NoteCollectRelation{Uid: testUser.Id})
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), relations, 2)
}

// TestCreateSpeechEvaluation 测试创建语音评估
func (suite *PlayerModelTestSuite) TestCreateSpeechEvaluation() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "playeruser007")

	evaluation := &model.SpeechEvaluation{
		Uid:        testUser.Id,
		ResourceId: "resource_001",
		Position:   120,
		Text:       "Hello world",
		AudioUrl:   "https://test.com/audio.mp3",
		Score:      85.5,
		Feedback:   "发音不错，语调需要改进",
		Timestamp:  time.Now().UnixMilli(),
	}

	err := suite.playerModel.SaveOne(evaluation)
	require.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), evaluation.Id)

	// 验证评估已保存
	var savedEvaluation model.SpeechEvaluation
	err = suite.helper.DB.First(&savedEvaluation, "id = ?", evaluation.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), testUser.Id, savedEvaluation.Uid)
	assert.Equal(suite.T(), "resource_001", savedEvaluation.ResourceId)
	assert.Equal(suite.T(), "Hello world", savedEvaluation.Text)
	assert.Equal(suite.T(), 85.5, savedEvaluation.Score)
	assert.Equal(suite.T(), "发音不错，语调需要改进", savedEvaluation.Feedback)
}

// TestGetUserSpeechEvaluations 测试获取用户语音评估
func (suite *PlayerModelTestSuite) TestGetUserSpeechEvaluations() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "playeruser008")

	// 创建多个语音评估
	evaluations := []*model.SpeechEvaluation{
		{
			Uid:        testUser.Id,
			ResourceId: "resource_001",
			Position:   60,
			Text:       "Hello",
			Score:      80.0,
			Timestamp:  time.Now().UnixMilli(),
		},
		{
			Uid:        testUser.Id,
			ResourceId: "resource_001",
			Position:   120,
			Text:       "World",
			Score:      90.0,
			Timestamp:  time.Now().UnixMilli() + 1000,
		},
	}

	for _, evaluation := range evaluations {
		err := suite.playerModel.SaveOne(evaluation)
		require.NoError(suite.T(), err)
	}

	// 获取用户的语音评估
	var userEvaluations []model.SpeechEvaluation
	err := suite.playerModel.GetOrderedList(&userEvaluations, "timestamp DESC", 
		model.SpeechEvaluation{Uid: testUser.Id})
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), userEvaluations, 2)

	// 验证按时间倒序排列
	assert.True(suite.T(), userEvaluations[0].Timestamp >= userEvaluations[1].Timestamp)
	assert.Equal(suite.T(), 90.0, userEvaluations[0].Score)
	assert.Equal(suite.T(), 80.0, userEvaluations[1].Score)
}

// TestCreateUserSubtitleRelation 测试创建用户字幕关系
func (suite *PlayerModelTestSuite) TestCreateUserSubtitleRelation() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "playeruser009")

	relation := &model.UserSubtitleRelation{
		Uid:          testUser.Id,
		ResourceId:   "resource_001",
		SubtitleType: "srt",
		SubtitleUrl:  "https://test.com/subtitle.srt",
		Language:     "en",
		IsDefault:    true,
		Timestamp:    time.Now().UnixMilli(),
	}

	err := suite.playerModel.SaveOne(relation)
	require.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), relation.Id)

	// 验证字幕关系已创建
	var savedRelation model.UserSubtitleRelation
	err = suite.helper.DB.First(&savedRelation, "uid = ? AND resource_id = ?", testUser.Id, "resource_001").Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), testUser.Id, savedRelation.Uid)
	assert.Equal(suite.T(), "resource_001", savedRelation.ResourceId)
	assert.Equal(suite.T(), "srt", savedRelation.SubtitleType)
	assert.Equal(suite.T(), "en", savedRelation.Language)
	assert.True(suite.T(), savedRelation.IsDefault)
}

// 运行测试套件
func TestPlayerModelSuite(t *testing.T) {
	suite.Run(t, new(PlayerModelTestSuite))
}
