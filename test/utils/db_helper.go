package utils

import (
	"fmt"
	"loop/internal/model"
	"loop/pkg/constants"
	"loop/pkg/timex"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
	"gorm.io/gorm"
)

// DBHelper 数据库测试辅助
type DBHelper struct {
	DB *gorm.DB
}

// NewDBHelper 创建数据库辅助实例
func NewDBHelper(db *gorm.DB) *DBHelper {
	return &DBHelper{DB: db}
}

// CreateTestUser 创建测试用户
func (h *DBHelper) CreateTestUser(t *testing.T, username string) *model.User {
	user := &model.User{
		Username:       username,
		PasswordDigest: "$2a$10$test.hash.password.for.testing.only",
		Nickname:       fmt.Sprintf("Test_%s", username),
		Status:         0, // 正常状态
		VipLevelId:     "1",
		NativeLangCode: "zh-CN",
		TargetLangCode: "en-US",
	}

	err := h.DB.Create(user).Error
	require.NoError(t, err)
	return user
}

// CreateTestVIP 创建测试VIP等级
func (h *DBHelper) CreateTestVIP(t *testing.T, name string, level int) *model.VIP {
	vip := &model.VIP{
		Name:  name,
		Level: level,
	}

	err := h.DB.Create(vip).Error
	require.NoError(t, err)
	return vip
}

// CreateTestUserVIPRelation 创建用户VIP关系
func (h *DBHelper) CreateTestUserVIPRelation(t *testing.T, uid string, vipID uint, days int) *model.UserVIPRelations {
	expireTime := time.Now().AddDate(0, 0, days)
	relation := &model.UserVIPRelations{
		Uid:             uid,
		IsVip:           1,
		VipID:           vipID,
		IsSubscription:  0,
		ExpireDate:      expireTime.Format("2006-01-02"),
		ExpireTimestamp: expireTime.UnixMilli(),
	}

	err := h.DB.Create(relation).Error
	require.NoError(t, err)
	return relation
}

// CreateTestTradeProduct 创建测试商品
func (h *DBHelper) CreateTestTradeProduct(t *testing.T, name string, price float64, days int, vipID int) *model.TradeProduct {
	product := &model.TradeProduct{
		Name:           name,
		Type:           1, // 会员商品
		IsSubscription: 1,
		Price:          price,
		OriginPrice:    price,
		IosProductId:   fmt.Sprintf("com.test.%s", name),
		Currency:       "USD",
		Terminal:       2, // iOS
		Days:           days,
		VipID:          uint(vipID),
	}

	err := h.DB.Create(product).Error
	require.NoError(t, err)
	return product
}

// CreateTestPromotionCode 创建测试兑换码
func (h *DBHelper) CreateTestPromotionCode(t *testing.T, code string, days int, vipLevel int) *model.PromotionCode {
	promotionCode := &model.PromotionCode{
		Code:              code,
		Days:              days,
		VipLevel:          vipLevel,
		Status:            0, // 未使用
		CreatedTimestamp:  timex.Now().UnixMilli(),
		RedeemedTimestamp: 0,
	}

	err := h.DB.Create(promotionCode).Error
	require.NoError(t, err)
	return promotionCode
}

// CreateTestBenefitGroup 创建测试权益组
func (h *DBHelper) CreateTestBenefitGroup(t *testing.T, code, name string) *model.BenefitGroup {
	group := &model.BenefitGroup{
		GroupCode:       constants.BenefitGroupCode(code),
		GroupName:       name,
		Description:     fmt.Sprintf("Test benefit group: %s", name),
		Status:          1, // 启用
		CreateTime:      time.Now(),
		CreateTimestamp: time.Now().UnixMilli(),
	}

	err := h.DB.Create(group).Error
	require.NoError(t, err)
	return group
}

// CreateTestBenefit 创建测试权益
func (h *DBHelper) CreateTestBenefit(t *testing.T, groupID uint, code, name string, benefitType int, defaultValue int) *model.Benefit {
	benefit := &model.Benefit{
		BenefitGroupID:  groupID,
		BenefitCode:     constants.BenefitCode(code),
		BenefitName:     name,
		Description:     fmt.Sprintf("Test benefit: %s", name),
		BenefitType:     benefitType,
		DefaultValue:    defaultValue,
		CreateTime:      time.Now(),
		CreateTimestamp: time.Now().UnixMilli(),
	}

	err := h.DB.Create(benefit).Error
	require.NoError(t, err)
	return benefit
}

// CreateTestUserBenefit 创建用户权益
func (h *DBHelper) CreateTestUserBenefit(t *testing.T, uid string, benefitID uint, currentValue, maxValue int) *model.UserBenefit {
	userBenefit := &model.UserBenefit{
		Uid:             uid,
		BenefitID:       benefitID,
		CurrentValue:    currentValue,
		MaxValue:        maxValue,
		LastResetTime:   time.Now(),
		CreateTime:      time.Now(),
		CreateTimestamp: time.Now().UnixMilli(),
	}

	err := h.DB.Create(userBenefit).Error
	require.NoError(t, err)
	return userBenefit
}

// CreateTestVipBenefit 创建VIP权益关联
func (h *DBHelper) CreateTestVipBenefit(t *testing.T, vipID, vipLevel, benefitGroupID, benefitID uint, benefitCode string) *model.VipBenefit {
	vipBenefit := &model.VipBenefit{
		VipID:           vipID,
		VipLevel:        vipLevel,
		BenefitGroupID:  benefitGroupID,
		BenefitID:       benefitID,
		BenefitCode:     constants.BenefitCode(benefitCode),
		CreateTime:      time.Now(),
		CreateTimestamp: time.Now().UnixMilli(),
	}

	err := h.DB.Create(vipBenefit).Error
	require.NoError(t, err)
	return vipBenefit
}

// CreateTestLearningPlan 创建测试学习计划
func (h *DBHelper) CreateTestLearningPlan(t *testing.T, uid, startLevel, targetLevel string, days int) *model.LearningPlan {
	now := time.Now()
	plan := &model.LearningPlan{
		Uid:            uid,
		StartLevel:     startLevel,
		TargetLevel:    targetLevel,
		StartTimestamp: timex.GetDateZeroTimestamp(now),
		EndTimestamp:   timex.GetDateZeroTimestamp(now.AddDate(0, 0, days)),
		Status:         1, // 进行中
		DailySentences: 10,
		TotalLearnDays: days,
	}

	err := h.DB.Create(plan).Error
	require.NoError(t, err)
	return plan
}

// CreateTestResource 创建测试资源
func (h *DBHelper) CreateTestResource(t *testing.T, title, level string, duration int) *model.Resource {
	resource := &model.Resource{
		Title:       title,
		Description: fmt.Sprintf("Test resource: %s", title),
		VideoUrl:    fmt.Sprintf("https://test.com/video/%s.mp4", title),
		SubtitleUrl: fmt.Sprintf("https://test.com/subtitle/%s.srt", title),
		Duration:    duration,
		Level:       level,
	}

	err := h.DB.Create(resource).Error
	require.NoError(t, err)
	return resource
}

// CreateTestSeries 创建测试系列
func (h *DBHelper) CreateTestSeries(t *testing.T, name string) *model.Series {
	series := &model.Series{
		Name:        name,
		Description: fmt.Sprintf("Test series: %s", name),
		CoverUrl:    fmt.Sprintf("https://test.com/cover/%s.jpg", name),
	}

	err := h.DB.Create(series).Error
	require.NoError(t, err)
	return series
}

// CreateTestCategory 创建测试分类
func (h *DBHelper) CreateTestCategory(t *testing.T, name string, categoryTypeID uint) *model.Category {
	category := &model.Category{
		Name:           name,
		Description:    fmt.Sprintf("Test category: %s", name),
		CategoryTypeId: categoryTypeID,
	}

	err := h.DB.Create(category).Error
	require.NoError(t, err)
	return category
}

// CreateTestCategoryType 创建测试分类类型
func (h *DBHelper) CreateTestCategoryType(t *testing.T, name string) *model.CategoryType {
	categoryType := &model.CategoryType{
		Name:        name,
		Description: fmt.Sprintf("Test category type: %s", name),
	}

	err := h.DB.Create(categoryType).Error
	require.NoError(t, err)
	return categoryType
}

// CreateTestUserPlayerConfig 创建用户播放器配置
func (h *DBHelper) CreateTestUserPlayerConfig(t *testing.T, uid string) *model.UserPlayerConfig {
	config := &model.UserPlayerConfig{
		Uid:                   uid,
		PlaybackSpeed:         1.0,
		SubtitleLanguage:      "en",
		AutoPlay:              true,
		SubtitleSize:          16,
		SubtitleColor:         "#FFFFFF",
		BackgroundColor:       "#000000",
		ShowTranslation:       true,
		RepeatMode:            0,
		VolumeLevel:           80,
		BrightnessLevel:       50,
		ShowProgressIndicator: true,
		AutoPause:             false,
		GestureControl:        true,
		PictureInPicture:      false,
	}

	err := h.DB.Create(config).Error
	require.NoError(t, err)
	return config
}

// CleanupTestData 清理测试数据
func (h *DBHelper) CleanupTestData(t *testing.T, tables ...string) {
	for _, table := range tables {
		err := h.DB.Exec(fmt.Sprintf("DELETE FROM %s WHERE id > 1000", table)).Error
		if err != nil {
			t.Logf("Warning: failed to cleanup table %s: %v", table, err)
		}
	}
}
