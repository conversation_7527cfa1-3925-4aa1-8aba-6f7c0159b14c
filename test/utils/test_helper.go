package utils

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"loop/internal/config"
	"loop/internal/model"
	"loop/pkg/dbx"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// TestHelper 测试辅助结构体
type TestHelper struct {
	DB     *gorm.DB
	Config *config.Config
	Router *gin.Engine
}

// NewTestHelper 创建测试辅助实例
func NewTestHelper(t *testing.T) *TestHelper {
	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 加载测试配置
	cfg := loadTestConfig(t)

	// 初始化测试数据库
	db := initTestDB(t, cfg)

	// 创建路由
	router := gin.New()

	return &TestHelper{
		DB:     db,
		Config: cfg,
		Router: router,
	}
}

// loadTestConfig 加载测试配置
func loadTestConfig(t *testing.T) *config.Config {
	// 获取项目根目录
	rootDir, err := findProjectRoot()
	require.NoError(t, err)

	configPath := filepath.Join(rootDir, "test", "config", "test_config.yaml")
	
	cfg, err := config.LoadConfig(configPath)
	require.NoError(t, err)

	return cfg
}

// initTestDB 初始化测试数据库
func initTestDB(t *testing.T, cfg *config.Config) *gorm.DB {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%d)/%s?charset=%s&parseTime=%t&loc=%s",
		cfg.Database.Username,
		cfg.Database.Password,
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.Database,
		cfg.Database.Charset,
		cfg.Database.ParseTime,
		cfg.Database.Loc,
	)

	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent), // 测试时静默日志
	})
	require.NoError(t, err)

	// 自动迁移表结构
	err = db.AutoMigrate(model.Models...)
	require.NoError(t, err)

	return db
}

// findProjectRoot 查找项目根目录
func findProjectRoot() (string, error) {
	dir, err := os.Getwd()
	if err != nil {
		return "", err
	}

	for {
		if _, err := os.Stat(filepath.Join(dir, "go.mod")); err == nil {
			return dir, nil
		}

		parent := filepath.Dir(dir)
		if parent == dir {
			break
		}
		dir = parent
	}

	return "", fmt.Errorf("could not find project root")
}

// CleanupDB 清理数据库
func (h *TestHelper) CleanupDB(t *testing.T) {
	// 清理所有测试数据
	tables := []string{
		"user_benefit_logs", "vip_benefits", "user_benefits", "benefits", "benefit_groups",
		"promotion_codes", "user_subscription_logs", "user_subscriptions", "user_purchase_orders",
		"trade_products", "user_vip_flows", "user_vip_relations", "vips",
		"learning_plan_days", "learning_plan_weeks", "learning_plan_stages", "learning_plans",
		"user_questionnaires", "user_feedbacks", "speech_evaluations",
		"user_remote_resource_relations", "user_local_resources", "user_subtitle_relations",
		"note_collect_relations", "notes", "watch_histories", "user_player_configs",
		"deleted_users", "users", "data_episode_eaches", "data_episodes",
		"featured_contents", "series_resource_relations", "category_resource_relations",
		"category_series_relations", "resource_relations", "resources", "series_relations",
		"series", "categories", "category_types", "sys_roles", "sys_users",
	}

	for _, table := range tables {
		h.DB.Exec(fmt.Sprintf("DELETE FROM %s WHERE id > 1000", table)) // 保留基础测试数据
	}
}

// CreateTestUser 创建测试用户
func (h *TestHelper) CreateTestUser(t *testing.T, username string) *model.User {
	user := &model.User{
		Username:       username,
		PasswordDigest: "$2a$10$test.hash.password",
		Nickname:       "Test " + username,
		Status:         0,
		VipLevelId:     "1",
	}

	err := h.DB.Create(user).Error
	require.NoError(t, err)

	return user
}

// CreateTestVIPRelation 创建测试VIP关系
func (h *TestHelper) CreateTestVIPRelation(t *testing.T, uid string, vipLevel int, days int) *model.UserVIPRelations {
	expireTime := time.Now().AddDate(0, 0, days)
	relation := &model.UserVIPRelations{
		Uid:             uid,
		IsVip:           1,
		VipID:           uint(vipLevel),
		IsSubscription:  0,
		ExpireDate:      expireTime.Format("2006-01-02"),
		ExpireTimestamp: expireTime.UnixMilli(),
	}

	err := h.DB.Create(relation).Error
	require.NoError(t, err)

	return relation
}

// MakeRequest 发送HTTP请求
func (h *TestHelper) MakeRequest(method, url string, body interface{}, headers map[string]string) *httptest.ResponseRecorder {
	var reqBody io.Reader
	if body != nil {
		jsonBody, _ := json.Marshal(body)
		reqBody = bytes.NewBuffer(jsonBody)
	}

	req := httptest.NewRequest(method, url, reqBody)
	
	// 设置默认头部
	req.Header.Set("Content-Type", "application/json")
	
	// 设置自定义头部
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	w := httptest.NewRecorder()
	h.Router.ServeHTTP(w, req)

	return w
}

// AssertJSONResponse 断言JSON响应
func (h *TestHelper) AssertJSONResponse(t *testing.T, w *httptest.ResponseRecorder, expectedCode int, expectedData interface{}) {
	assert.Equal(t, expectedCode, w.Code)
	assert.Equal(t, "application/json; charset=utf-8", w.Header().Get("Content-Type"))

	if expectedData != nil {
		var actualData interface{}
		err := json.Unmarshal(w.Body.Bytes(), &actualData)
		require.NoError(t, err)

		expectedJSON, _ := json.Marshal(expectedData)
		actualJSON, _ := json.Marshal(actualData)
		assert.JSONEq(t, string(expectedJSON), string(actualJSON))
	}
}

// GenerateJWT 生成测试JWT token
func (h *TestHelper) GenerateJWT(t *testing.T, uid, username string, status int) string {
	// 这里应该使用项目中的JWT生成逻辑
	// 暂时返回一个模拟的token
	return fmt.Sprintf("Bearer test.jwt.token.%s", uid)
}

// WithTransaction 在事务中执行测试
func (h *TestHelper) WithTransaction(t *testing.T, fn func(tx *gorm.DB)) {
	tx := h.DB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			panic(r)
		}
	}()

	fn(tx)
	tx.Rollback() // 测试结束后回滚
}

// LoadFixture 加载测试数据
func (h *TestHelper) LoadFixture(t *testing.T, filename string, dest interface{}) {
	rootDir, err := findProjectRoot()
	require.NoError(t, err)

	fixturePath := filepath.Join(rootDir, "test", "fixtures", filename)
	data, err := os.ReadFile(fixturePath)
	require.NoError(t, err)

	err = json.Unmarshal(data, dest)
	require.NoError(t, err)
}
