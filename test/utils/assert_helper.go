package utils

import (
	"encoding/json"
	"fmt"
	"loop/pkg/web"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// AssertHelper 断言辅助结构体
type AssertHelper struct {
	t *testing.T
}

// NewAssertHelper 创建断言辅助实例
func NewAssertHelper(t *testing.T) *AssertHelper {
	return &AssertHelper{t: t}
}

// AssertSuccessResponse 断言成功响应
func (h *AssertHelper) AssertSuccessResponse(w *httptest.ResponseRecorder, expectedData interface{}) {
	assert.Equal(h.t, 200, w.Code, "Expected HTTP 200 OK")
	
	var response web.JsonResult
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(h.t, err, "Failed to unmarshal response")
	
	assert.Equal(h.t, web.SUCCESS, response.Code, "Expected success code")
	assert.Equal(h.t, "success", response.Msg, "Expected success message")
	
	if expectedData != nil {
		expectedJSON, _ := json.Marshal(expectedData)
		actualJSON, _ := json.Marshal(response.Data)
		assert.JSONEq(h.t, string(expectedJSON), string(actualJSON), "Response data mismatch")
	}
}

// AssertErrorResponse 断言错误响应
func (h *AssertHelper) AssertErrorResponse(w *httptest.ResponseRecorder, expectedCode int, expectedMsg string) {
	var response web.JsonResult
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(h.t, err, "Failed to unmarshal response")
	
	assert.Equal(h.t, expectedCode, w.Code, "HTTP status code mismatch")
	assert.NotEqual(h.t, web.SUCCESS, response.Code, "Expected error code")
	
	if expectedMsg != "" {
		assert.Contains(h.t, response.Msg, expectedMsg, "Error message mismatch")
	}
}

// AssertValidationError 断言参数验证错误
func (h *AssertHelper) AssertValidationError(w *httptest.ResponseRecorder) {
	h.AssertErrorResponse(w, 400, "")
}

// AssertUnauthorizedError 断言未授权错误
func (h *AssertHelper) AssertUnauthorizedError(w *httptest.ResponseRecorder) {
	h.AssertErrorResponse(w, 401, "")
}

// AssertForbiddenError 断言禁止访问错误
func (h *AssertHelper) AssertForbiddenError(w *httptest.ResponseRecorder) {
	h.AssertErrorResponse(w, 403, "")
}

// AssertNotFoundError 断言资源不存在错误
func (h *AssertHelper) AssertNotFoundError(w *httptest.ResponseRecorder) {
	h.AssertErrorResponse(w, 404, "")
}

// AssertInternalServerError 断言服务器内部错误
func (h *AssertHelper) AssertInternalServerError(w *httptest.ResponseRecorder) {
	h.AssertErrorResponse(w, 500, "")
}

// AssertEmptyResponse 断言空数据响应
func (h *AssertHelper) AssertEmptyResponse(w *httptest.ResponseRecorder) {
	assert.Equal(h.t, 200, w.Code, "Expected HTTP 200 OK")
	
	var response web.JsonResult
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(h.t, err, "Failed to unmarshal response")
	
	assert.Equal(h.t, web.SUCCESS, response.Code, "Expected success code")
	assert.Nil(h.t, response.Data, "Expected empty data")
}

// AssertListResponse 断言列表响应
func (h *AssertHelper) AssertListResponse(w *httptest.ResponseRecorder, expectedCount int) {
	assert.Equal(h.t, 200, w.Code, "Expected HTTP 200 OK")
	
	var response web.JsonResult
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(h.t, err, "Failed to unmarshal response")
	
	assert.Equal(h.t, web.SUCCESS, response.Code, "Expected success code")
	
	// 检查数据是否为数组
	dataArray, ok := response.Data.([]interface{})
	require.True(h.t, ok, "Expected data to be an array")
	
	if expectedCount >= 0 {
		assert.Equal(h.t, expectedCount, len(dataArray), "List count mismatch")
	}
}

// AssertPageResponse 断言分页响应
func (h *AssertHelper) AssertPageResponse(w *httptest.ResponseRecorder, expectedTotal int64) {
	assert.Equal(h.t, 200, w.Code, "Expected HTTP 200 OK")
	
	var response web.JsonResult
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(h.t, err, "Failed to unmarshal response")
	
	assert.Equal(h.t, web.SUCCESS, response.Code, "Expected success code")
	
	// 检查分页数据结构
	dataMap, ok := response.Data.(map[string]interface{})
	require.True(h.t, ok, "Expected data to be a map")
	
	assert.Contains(h.t, dataMap, "list", "Expected 'list' field in page response")
	assert.Contains(h.t, dataMap, "total", "Expected 'total' field in page response")
	
	if expectedTotal >= 0 {
		total, ok := dataMap["total"].(float64) // JSON numbers are float64
		require.True(h.t, ok, "Expected total to be a number")
		assert.Equal(h.t, expectedTotal, int64(total), "Total count mismatch")
	}
}

// AssertJSONField 断言JSON字段值
func (h *AssertHelper) AssertJSONField(w *httptest.ResponseRecorder, fieldPath string, expectedValue interface{}) {
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(h.t, err, "Failed to unmarshal response")
	
	actualValue := getNestedField(response, fieldPath)
	assert.Equal(h.t, expectedValue, actualValue, fmt.Sprintf("Field %s value mismatch", fieldPath))
}

// AssertTimeField 断言时间字段
func (h *AssertHelper) AssertTimeField(w *httptest.ResponseRecorder, fieldPath string, expectedTime time.Time, tolerance time.Duration) {
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(h.t, err, "Failed to unmarshal response")
	
	timeStr, ok := getNestedField(response, fieldPath).(string)
	require.True(h.t, ok, fmt.Sprintf("Expected field %s to be a string", fieldPath))
	
	actualTime, err := time.Parse(time.RFC3339, timeStr)
	require.NoError(h.t, err, fmt.Sprintf("Failed to parse time field %s", fieldPath))
	
	diff := actualTime.Sub(expectedTime)
	if diff < 0 {
		diff = -diff
	}
	
	assert.True(h.t, diff <= tolerance, 
		fmt.Sprintf("Time field %s differs by %v, expected within %v", fieldPath, diff, tolerance))
}

// AssertContainsField 断言包含字段
func (h *AssertHelper) AssertContainsField(w *httptest.ResponseRecorder, fieldPath string) {
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(h.t, err, "Failed to unmarshal response")
	
	value := getNestedField(response, fieldPath)
	assert.NotNil(h.t, value, fmt.Sprintf("Expected field %s to exist", fieldPath))
}

// AssertNotContainsField 断言不包含字段
func (h *AssertHelper) AssertNotContainsField(w *httptest.ResponseRecorder, fieldPath string) {
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(h.t, err, "Failed to unmarshal response")
	
	value := getNestedField(response, fieldPath)
	assert.Nil(h.t, value, fmt.Sprintf("Expected field %s to not exist", fieldPath))
}

// AssertArrayLength 断言数组长度
func (h *AssertHelper) AssertArrayLength(w *httptest.ResponseRecorder, fieldPath string, expectedLength int) {
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(h.t, err, "Failed to unmarshal response")
	
	arrayValue := getNestedField(response, fieldPath)
	array, ok := arrayValue.([]interface{})
	require.True(h.t, ok, fmt.Sprintf("Expected field %s to be an array", fieldPath))
	
	assert.Equal(h.t, expectedLength, len(array), fmt.Sprintf("Array %s length mismatch", fieldPath))
}

// getNestedField 获取嵌套字段值
func getNestedField(data map[string]interface{}, fieldPath string) interface{} {
	// 简单实现，支持点分隔的字段路径，如 "data.user.name"
	// 这里可以根据需要扩展更复杂的路径解析
	
	// 暂时只支持单层字段
	return data[fieldPath]
}

// AssertResponseHeaders 断言响应头
func (h *AssertHelper) AssertResponseHeaders(w *httptest.ResponseRecorder, expectedHeaders map[string]string) {
	for key, expectedValue := range expectedHeaders {
		actualValue := w.Header().Get(key)
		assert.Equal(h.t, expectedValue, actualValue, fmt.Sprintf("Header %s mismatch", key))
	}
}

// AssertContentType 断言内容类型
func (h *AssertHelper) AssertContentType(w *httptest.ResponseRecorder, expectedContentType string) {
	actualContentType := w.Header().Get("Content-Type")
	assert.Equal(h.t, expectedContentType, actualContentType, "Content-Type mismatch")
}
