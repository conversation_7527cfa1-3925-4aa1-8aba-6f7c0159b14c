package user

import (
	"loop/internal/config"
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/request"
	"loop/pkg/jwtx"
	"loop/pkg/web"
	"loop/test/utils"
	"net/http"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"golang.org/x/crypto/bcrypt"
)

// UserRepoTestSuite 用户数据层测试套件
type UserRepoTestSuite struct {
	suite.Suite
	helper   *utils.TestHelper
	dbHelper *utils.DBHelper
	userRepo *data.UserRepo
	ctx      *gin.Context
}

// SetupSuite 设置测试套件
func (suite *UserRepoTestSuite) SetupSuite() {
	suite.helper = utils.NewTestHelper(suite.T())
	suite.dbHelper = utils.NewDBHelper(suite.helper.DB)

	// 创建用户仓库实例
	dbModel := &model.DbModel{DBExtension: *suite.helper.DB}
	userModel := model.NewUserModel(dbModel, suite.helper.Config)
	suite.userRepo = data.NewUserRepo(userModel, suite.helper.Config)

	// 创建测试上下文
	suite.ctx, _ = gin.CreateTestContext(nil)
}

// TearDownSuite 清理测试套件
func (suite *UserRepoTestSuite) TearDownSuite() {
	suite.helper.CleanupDB(suite.T())
}

// SetupTest 每个测试前的设置
func (suite *UserRepoTestSuite) SetupTest() {
	// 清理测试数据
	suite.helper.DB.Exec("DELETE FROM users WHERE id > 1000")
	suite.helper.DB.Exec("DELETE FROM user_player_configs WHERE uid LIKE '10%'")
	suite.helper.DB.Exec("DELETE FROM watch_histories WHERE uid LIKE '10%'")
	suite.helper.DB.Exec("DELETE FROM deleted_users WHERE uid LIKE '10%'")
}

// TestRegister 测试用户注册
func (suite *UserRepoTestSuite) TestRegister() {
	req := request.UserRegisterReq{
		Username: "newuser001",
		Password: "password123",
		Nickname: "New User 001",
	}

	result := suite.userRepo.Register(suite.ctx, req)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证用户已创建
	var user model.User
	err := suite.helper.DB.First(&user, "username = ?", req.Username).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), req.Username, user.Username)
	assert.Equal(suite.T(), req.Nickname, user.Nickname)

	// 验证密码已加密
	err = bcrypt.CompareHashAndPassword([]byte(user.PasswordDigest), []byte(req.Password))
	assert.NoError(suite.T(), err)
}

// TestRegisterDuplicateUsername 测试注册重复用户名
func (suite *UserRepoTestSuite) TestRegisterDuplicateUsername() {
	// 先创建一个用户
	existingUser := suite.dbHelper.CreateTestUser(suite.T(), "existinguser")

	// 尝试注册相同用户名
	req := request.UserRegisterReq{
		Username: existingUser.Username,
		Password: "password123",
		Nickname: "Another User",
	}

	result := suite.userRepo.Register(suite.ctx, req)
	require.NotNil(suite.T(), result)
	assert.NotEqual(suite.T(), web.SUCCESS, result.Code)
	assert.Contains(suite.T(), result.Msg, "用户名已被使用") // 根据实际错误消息调整
}

// TestRegisterDuplicateNickname 测试注册重复昵称
func (suite *UserRepoTestSuite) TestRegisterDuplicateNickname() {
	// 先创建一个用户
	existingUser := suite.dbHelper.CreateTestUser(suite.T(), "user001")

	// 尝试注册相同昵称
	req := request.UserRegisterReq{
		Username: "newuser002",
		Password: "password123",
		Nickname: existingUser.Nickname,
	}

	result := suite.userRepo.Register(suite.ctx, req)
	require.NotNil(suite.T(), result)
	assert.NotEqual(suite.T(), web.SUCCESS, result.Code)
	assert.Contains(suite.T(), result.Msg, "昵称已被使用") // 根据实际错误消息调整
}

// TestLogin 测试用户登录
func (suite *UserRepoTestSuite) TestLogin() {
	// 创建测试用户
	password := "password123"
	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	
	user := &model.User{
		Username:       "loginuser001",
		PasswordDigest: string(hashedPassword),
		Nickname:       "Login User 001",
		Status:         0,
		VipLevelId:     "1",
	}
	err := suite.helper.DB.Create(user).Error
	require.NoError(suite.T(), err)

	// 测试正确登录
	req := request.UserLoginReq{
		Username: "loginuser001",
		Password: password,
	}

	result := suite.userRepo.Login(suite.ctx, req)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证返回数据包含token和用户信息
	data, ok := result.Data.(map[string]interface{})
	require.True(suite.T(), ok)
	assert.Contains(suite.T(), data, "token")
	assert.Contains(suite.T(), data, "user")
}

// TestLoginWrongPassword 测试错误密码登录
func (suite *UserRepoTestSuite) TestLoginWrongPassword() {
	// 创建测试用户
	password := "password123"
	hashedPassword, _ := bcrypt.GenerateFromPassword([]byte(password), bcrypt.DefaultCost)
	
	user := &model.User{
		Username:       "loginuser002",
		PasswordDigest: string(hashedPassword),
		Nickname:       "Login User 002",
		Status:         0,
		VipLevelId:     "1",
	}
	err := suite.helper.DB.Create(user).Error
	require.NoError(suite.T(), err)

	// 测试错误密码
	req := request.UserLoginReq{
		Username: "loginuser002",
		Password: "wrongpassword",
	}

	result := suite.userRepo.Login(suite.ctx, req)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), http.StatusUnauthorized, result.Code)
}

// TestLoginNonexistentUser 测试登录不存在的用户
func (suite *UserRepoTestSuite) TestLoginNonexistentUser() {
	req := request.UserLoginReq{
		Username: "nonexistentuser",
		Password: "password123",
	}

	result := suite.userRepo.Login(suite.ctx, req)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), http.StatusUnauthorized, result.Code)
}

// TestLoginByApple 测试Apple登录
func (suite *UserRepoTestSuite) TestLoginByApple() {
	req := request.AppleLoginReq{
		AppleUserIdentifier: "apple_test_id_001",
		Email:              "<EMAIL>",
		FullName:           "Apple Test User",
	}

	result := suite.userRepo.LoginByApple(suite.ctx, req)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证用户已创建
	var user model.User
	err := suite.helper.DB.First(&user, "apple_user_identifier = ?", req.AppleUserIdentifier).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), req.AppleUserIdentifier, user.AppleUserIdentifier)
}

// TestLoginByAppleExistingUser 测试已存在Apple用户的登录
func (suite *UserRepoTestSuite) TestLoginByAppleExistingUser() {
	// 先创建Apple用户
	user := &model.User{
		Username:            "appleuser001",
		PasswordDigest:      "",
		Nickname:            "Apple User 001",
		Status:              0,
		AppleUserIdentifier: "existing_apple_id",
		VipLevelId:          "1",
	}
	err := suite.helper.DB.Create(user).Error
	require.NoError(suite.T(), err)

	// 使用相同Apple ID登录
	req := request.AppleLoginReq{
		AppleUserIdentifier: "existing_apple_id",
		Email:              "<EMAIL>",
		FullName:           "Apple Test User",
	}

	result := suite.userRepo.LoginByApple(suite.ctx, req)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证没有创建新用户
	var count int64
	suite.helper.DB.Model(&model.User{}).Where("apple_user_identifier = ?", req.AppleUserIdentifier).Count(&count)
	assert.Equal(suite.T(), int64(1), count)
}

// TestSetInfo 测试设置用户信息
func (suite *UserRepoTestSuite) TestSetInfo() {
	// 创建测试用户
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "setinfouser001")

	req := request.UpdateUserInfoReq{
		Id:             testUser.Id,
		Nickname:       "Updated Nickname",
		Avatar:         "https://test.com/new_avatar.jpg",
		NativeLangCode: "en-US",
		TargetLangCode: "zh-CN",
	}

	result := suite.userRepo.SetInfo(suite.ctx, req)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证用户信息已更新
	var updatedUser model.User
	err := suite.helper.DB.First(&updatedUser, "id = ?", testUser.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), req.Nickname, updatedUser.Nickname)
	assert.Equal(suite.T(), req.Avatar, updatedUser.Avatar)
	assert.Equal(suite.T(), req.NativeLangCode, updatedUser.NativeLangCode)
	assert.Equal(suite.T(), req.TargetLangCode, updatedUser.TargetLangCode)
}

// TestFetchConfig 测试获取用户配置
func (suite *UserRepoTestSuite) TestFetchConfig() {
	// 创建测试用户和配置
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "configuser001")
	config := suite.dbHelper.CreateTestUserPlayerConfig(suite.T(), testUser.Id)

	// 设置JWT上下文
	suite.ctx.Set("uid", testUser.Id)

	result := suite.userRepo.FetchConfig(suite.ctx)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证返回的配置数据
	data, ok := result.Data.(map[string]interface{})
	require.True(suite.T(), ok)
	assert.Equal(suite.T(), config.PlaybackSpeed, data["playback_speed"])
	assert.Equal(suite.T(), config.SubtitleLanguage, data["subtitle_language"])
}

// TestUpdateUserPlayerConfig 测试更新播放器配置
func (suite *UserRepoTestSuite) TestUpdateUserPlayerConfig() {
	// 创建测试用户
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "updateconfiguser001")

	// 设置JWT上下文
	suite.ctx.Set("uid", testUser.Id)

	req := request.UpdateUserPlayerConfigReq{
		PlaybackSpeed:    1.5,
		SubtitleLanguage: "zh",
		AutoPlay:         false,
		SubtitleSize:     20,
		VolumeLevel:      90,
	}

	result := suite.userRepo.UpdateUserPlayerConfig(suite.ctx, req)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证配置已更新
	var config model.UserPlayerConfig
	err := suite.helper.DB.First(&config, "uid = ?", testUser.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), req.PlaybackSpeed, config.PlaybackSpeed)
	assert.Equal(suite.T(), req.SubtitleLanguage, config.SubtitleLanguage)
	assert.Equal(suite.T(), req.AutoPlay, config.AutoPlay)
}

// TestGetWatchHistory 测试获取观看历史
func (suite *UserRepoTestSuite) TestGetWatchHistory() {
	// 创建测试用户
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "historyuser001")

	// 创建观看历史
	histories := []*model.WatchHistory{
		{
			Uid:            testUser.Id,
			ResourceId:     "resource_001",
			ResourceType:   1,
			Position:       120,
			Duration:       300,
			WatchTimestamp: 1703001600000,
		},
		{
			Uid:            testUser.Id,
			ResourceId:     "resource_002",
			ResourceType:   1,
			Position:       60,
			Duration:       240,
			WatchTimestamp: 1703002600000,
		},
	}

	for _, history := range histories {
		err := suite.helper.DB.Create(history).Error
		require.NoError(suite.T(), err)
	}

	// 设置JWT上下文
	suite.ctx.Set("uid", testUser.Id)

	result := suite.userRepo.GetWatchHistory(suite.ctx)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证返回的历史数据
	data, ok := result.Data.([]interface{})
	require.True(suite.T(), ok)
	assert.Len(suite.T(), data, 2)
}

// TestDeleteWatchHistory 测试删除观看历史
func (suite *UserRepoTestSuite) TestDeleteWatchHistory() {
	// 创建测试用户和观看历史
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "deletehistoryuser001")
	
	history := &model.WatchHistory{
		Uid:            testUser.Id,
		ResourceId:     "resource_001",
		ResourceType:   1,
		Position:       120,
		Duration:       300,
		WatchTimestamp: 1703001600000,
	}
	err := suite.helper.DB.Create(history).Error
	require.NoError(suite.T(), err)

	// 设置JWT上下文
	suite.ctx.Set("uid", testUser.Id)

	req := request.DeleteWatchHistoryReq{
		Ids: []string{history.Id},
	}

	result := suite.userRepo.DeleteWatchHistory(suite.ctx, req)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证历史记录已删除
	var count int64
	suite.helper.DB.Model(&model.WatchHistory{}).Where("id = ?", history.Id).Count(&count)
	assert.Equal(suite.T(), int64(0), count)
}

// TestDeleteAccount 测试删除账户
func (suite *UserRepoTestSuite) TestDeleteAccount() {
	// 创建测试用户
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "deleteaccountuser001")

	// 设置JWT上下文
	suite.ctx.Set("uid", testUser.Id)

	result := suite.userRepo.DeleteAccount(suite.ctx)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证用户状态已更新为已注销
	var deletedUser model.User
	err := suite.helper.DB.First(&deletedUser, "id = ?", testUser.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 2, deletedUser.Status) // 2表示已注销

	// 验证创建了删除记录
	var deletedRecord model.DeletedUser
	err = suite.helper.DB.First(&deletedRecord, "uid = ?", testUser.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), testUser.Id, deletedRecord.Uid)
}

// 运行测试套件
func TestUserRepoSuite(t *testing.T) {
	suite.Run(t, new(UserRepoTestSuite))
}
