package user

import (
	"bytes"
	"encoding/json"
	"loop/internal/api"
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/request"
	"loop/pkg/jwtx"
	"loop/test/utils"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// UserApiTestSuite 用户API测试套件
type UserApiTestSuite struct {
	suite.Suite
	helper      *utils.TestHelper
	dbHelper    *utils.DBHelper
	assertHelper *utils.AssertHelper
	userApi     *api.UserApi
	router      *gin.Engine
}

// SetupSuite 设置测试套件
func (suite *UserApiTestSuite) SetupSuite() {
	suite.helper = utils.NewTestHelper(suite.T())
	suite.dbHelper = utils.NewDBHelper(suite.helper.DB)
	suite.assertHelper = utils.NewAssertHelper(suite.T())

	// 创建API实例
	dbModel := &model.DbModel{DBExtension: *suite.helper.DB}
	userModel := model.NewUserModel(dbModel, suite.helper.Config)
	userRepo := data.NewUserRepo(userModel, suite.helper.Config)
	settingRepo := data.NewSettingRepo() // 假设有这个仓库
	suite.userApi = api.NewUserApi(userRepo, settingRepo, dbModel)

	// 设置路由
	suite.router = gin.New()
	suite.setupRoutes()
}

// setupRoutes 设置测试路由
func (suite *UserApiTestSuite) setupRoutes() {
	v1 := suite.router.Group("/api/v1")
	
	// 公开路由
	v1.POST("/register", suite.userApi.Register)
	v1.POST("/login", suite.userApi.Login)
	v1.POST("/login/apple", suite.userApi.LoginByApple)

	// 需要认证的路由
	protected := v1.Group("/")
	protected.Use(suite.mockJWTMiddleware())
	{
		protected.PUT("/user", suite.userApi.Update)
		protected.POST("/user/logout", suite.userApi.Logout)
		protected.GET("/user/config", suite.userApi.FetchConfig)
		protected.PUT("/user/playerConfig", suite.userApi.UpdateUserPlayerConfig)
		protected.GET("/user/watchHistory", suite.userApi.GetWatchHistory)
		protected.DELETE("/user/watchHistory", suite.userApi.DeleteWatchHistory)
		protected.POST("/user/deleteAccount", suite.userApi.DeleteAccount)
	}
}

// mockJWTMiddleware 模拟JWT中间件
func (suite *UserApiTestSuite) mockJWTMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从请求头获取测试用户ID
		uid := c.GetHeader("X-Test-UID")
		if uid != "" {
			c.Set("uid", uid)
			c.Set("username", "testuser")
			c.Set("status", 0)
		}
		c.Next()
	}
}

// TearDownSuite 清理测试套件
func (suite *UserApiTestSuite) TearDownSuite() {
	suite.helper.CleanupDB(suite.T())
}

// SetupTest 每个测试前的设置
func (suite *UserApiTestSuite) SetupTest() {
	// 清理测试数据
	suite.helper.DB.Exec("DELETE FROM users WHERE id > 1000")
	suite.helper.DB.Exec("DELETE FROM user_player_configs WHERE uid LIKE '10%'")
	suite.helper.DB.Exec("DELETE FROM watch_histories WHERE uid LIKE '10%'")
	suite.helper.DB.Exec("DELETE FROM deleted_users WHERE uid LIKE '10%'")
}

// TestRegisterAPI 测试注册API
func (suite *UserApiTestSuite) TestRegisterAPI() {
	req := request.UserRegisterReq{
		Username: "apiuser001",
		Password: "password123",
		Nickname: "API User 001",
	}

	jsonData, _ := json.Marshal(req)
	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("POST", "/api/v1/register", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证用户已创建
	var user model.User
	err := suite.helper.DB.First(&user, "username = ?", req.Username).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), req.Username, user.Username)
}

// TestRegisterAPIInvalidData 测试注册API无效数据
func (suite *UserApiTestSuite) TestRegisterAPIInvalidData() {
	// 测试空用户名
	req := request.UserRegisterReq{
		Username: "",
		Password: "password123",
		Nickname: "API User 001",
	}

	jsonData, _ := json.Marshal(req)
	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("POST", "/api/v1/register", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertValidationError(w)
}

// TestLoginAPI 测试登录API
func (suite *UserApiTestSuite) TestLoginAPI() {
	// 先创建用户
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "loginapiuser001")

	req := request.UserLoginReq{
		Username: testUser.Username,
		Password: "password123", // 这应该与CreateTestUser中的密码匹配
	}

	jsonData, _ := json.Marshal(req)
	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("POST", "/api/v1/login", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")

	suite.router.ServeHTTP(w, httpReq)

	// 注意：这里可能需要根据实际的密码处理逻辑调整
	// 如果CreateTestUser使用的是固定的测试密码哈希，可能需要调整
	suite.assertHelper.AssertSuccessResponse(w, nil)
}

// TestLoginAPIWrongCredentials 测试登录API错误凭据
func (suite *UserApiTestSuite) TestLoginAPIWrongCredentials() {
	req := request.UserLoginReq{
		Username: "nonexistentuser",
		Password: "wrongpassword",
	}

	jsonData, _ := json.Marshal(req)
	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("POST", "/api/v1/login", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertUnauthorizedError(w)
}

// TestAppleLoginAPI 测试Apple登录API
func (suite *UserApiTestSuite) TestAppleLoginAPI() {
	req := request.AppleLoginReq{
		AppleUserIdentifier: "apple_api_test_001",
		Email:              "<EMAIL>",
		FullName:           "Apple API Test User",
	}

	jsonData, _ := json.Marshal(req)
	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("POST", "/api/v1/login/apple", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证用户已创建
	var user model.User
	err := suite.helper.DB.First(&user, "apple_user_identifier = ?", req.AppleUserIdentifier).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), req.AppleUserIdentifier, user.AppleUserIdentifier)
}

// TestUpdateUserAPI 测试更新用户信息API
func (suite *UserApiTestSuite) TestUpdateUserAPI() {
	// 创建测试用户
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "updateapiuser001")

	req := request.UpdateUserInfoReq{
		Nickname:       "Updated API User",
		Avatar:         "https://test.com/new_avatar.jpg",
		NativeLangCode: "en-US",
		TargetLangCode: "zh-CN",
	}

	jsonData, _ := json.Marshal(req)
	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("PUT", "/api/v1/user", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("X-Test-UID", testUser.Id) // 设置测试用户ID

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证用户信息已更新
	var updatedUser model.User
	err := suite.helper.DB.First(&updatedUser, "id = ?", testUser.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), req.Nickname, updatedUser.Nickname)
	assert.Equal(suite.T(), req.Avatar, updatedUser.Avatar)
}

// TestFetchConfigAPI 测试获取用户配置API
func (suite *UserApiTestSuite) TestFetchConfigAPI() {
	// 创建测试用户和配置
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "configapiuser001")
	config := suite.dbHelper.CreateTestUserPlayerConfig(suite.T(), testUser.Id)

	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("GET", "/api/v1/user/config", nil)
	httpReq.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证返回的配置数据
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)

	data, ok := response["data"].(map[string]interface{})
	require.True(suite.T(), ok)
	assert.Equal(suite.T(), config.PlaybackSpeed, data["playback_speed"])
}

// TestUpdatePlayerConfigAPI 测试更新播放器配置API
func (suite *UserApiTestSuite) TestUpdatePlayerConfigAPI() {
	// 创建测试用户
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "updateconfigapiuser001")

	req := request.UpdateUserPlayerConfigReq{
		PlaybackSpeed:    1.5,
		SubtitleLanguage: "zh",
		AutoPlay:         false,
		SubtitleSize:     20,
		VolumeLevel:      90,
	}

	jsonData, _ := json.Marshal(req)
	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("PUT", "/api/v1/user/playerConfig", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证配置已更新
	var config model.UserPlayerConfig
	err := suite.helper.DB.First(&config, "uid = ?", testUser.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), req.PlaybackSpeed, config.PlaybackSpeed)
	assert.Equal(suite.T(), req.SubtitleLanguage, config.SubtitleLanguage)
}

// TestGetWatchHistoryAPI 测试获取观看历史API
func (suite *UserApiTestSuite) TestGetWatchHistoryAPI() {
	// 创建测试用户和观看历史
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "historyapiuser001")

	histories := []*model.WatchHistory{
		{
			Uid:            testUser.Id,
			ResourceId:     "resource_001",
			ResourceType:   1,
			Position:       120,
			Duration:       300,
			WatchTimestamp: 1703001600000,
		},
		{
			Uid:            testUser.Id,
			ResourceId:     "resource_002",
			ResourceType:   1,
			Position:       60,
			Duration:       240,
			WatchTimestamp: 1703002600000,
		},
	}

	for _, history := range histories {
		err := suite.helper.DB.Create(history).Error
		require.NoError(suite.T(), err)
	}

	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("GET", "/api/v1/user/watchHistory", nil)
	httpReq.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertSuccessResponse(w, nil)
	suite.assertHelper.AssertListResponse(w, 2)
}

// TestDeleteWatchHistoryAPI 测试删除观看历史API
func (suite *UserApiTestSuite) TestDeleteWatchHistoryAPI() {
	// 创建测试用户和观看历史
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "deletehistoryapiuser001")

	history := &model.WatchHistory{
		Uid:            testUser.Id,
		ResourceId:     "resource_001",
		ResourceType:   1,
		Position:       120,
		Duration:       300,
		WatchTimestamp: 1703001600000,
	}
	err := suite.helper.DB.Create(history).Error
	require.NoError(suite.T(), err)

	req := request.DeleteWatchHistoryReq{
		Ids: []string{history.Id},
	}

	jsonData, _ := json.Marshal(req)
	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("DELETE", "/api/v1/user/watchHistory", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证历史记录已删除
	var count int64
	suite.helper.DB.Model(&model.WatchHistory{}).Where("id = ?", history.Id).Count(&count)
	assert.Equal(suite.T(), int64(0), count)
}

// TestDeleteAccountAPI 测试删除账户API
func (suite *UserApiTestSuite) TestDeleteAccountAPI() {
	// 创建测试用户
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "deleteaccountapiuser001")

	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("POST", "/api/v1/user/deleteAccount", nil)
	httpReq.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证用户状态已更新为已注销
	var deletedUser model.User
	err := suite.helper.DB.First(&deletedUser, "id = ?", testUser.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 2, deletedUser.Status) // 2表示已注销
}

// TestUnauthorizedAccess 测试未授权访问
func (suite *UserApiTestSuite) TestUnauthorizedAccess() {
	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("GET", "/api/v1/user/config", nil)
	// 不设置X-Test-UID头部

	suite.router.ServeHTTP(w, httpReq)

	// 根据实际的中间件实现，这里可能需要调整期望的状态码
	// 如果中间件返回401，使用AssertUnauthorizedError
	// 如果返回其他错误，相应调整
	assert.NotEqual(suite.T(), http.StatusOK, w.Code)
}

// 运行测试套件
func TestUserApiSuite(t *testing.T) {
	suite.Run(t, new(UserApiTestSuite))
}
