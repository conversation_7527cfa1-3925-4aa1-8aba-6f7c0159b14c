package user

import (
	"loop/internal/config"
	"loop/internal/model"
	"loop/test/utils"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// UserModelTestSuite 用户模型测试套件
type UserModelTestSuite struct {
	suite.Suite
	helper    *utils.TestHelper
	dbHelper  *utils.DBHelper
	userModel *model.UserModel
}

// SetupSuite 设置测试套件
func (suite *UserModelTestSuite) SetupSuite() {
	suite.helper = utils.NewTestHelper(suite.T())
	suite.dbHelper = utils.NewDBHelper(suite.helper.DB)
	
	// 创建用户模型实例
	dbModel := &model.DbModel{DBExtension: *suite.helper.DB}
	suite.userModel = model.NewUserModel(dbModel, suite.helper.Config)
}

// TearDownSuite 清理测试套件
func (suite *UserModelTestSuite) TearDownSuite() {
	suite.helper.CleanupDB(suite.T())
}

// SetupTest 每个测试前的设置
func (suite *UserModelTestSuite) SetupTest() {
	// 清理测试数据
	suite.helper.DB.Exec("DELETE FROM users WHERE id > 1000")
	suite.helper.DB.Exec("DELETE FROM user_player_configs WHERE uid LIKE '10%'")
	suite.helper.DB.Exec("DELETE FROM watch_histories WHERE uid LIKE '10%'")
}

// TestCreateUser 测试创建用户
func (suite *UserModelTestSuite) TestCreateUser() {
	user := &model.User{
		Username:       "testuser001",
		PasswordDigest: "$2a$10$test.hash.password",
		Nickname:       "Test User 001",
		Status:         0,
		VipLevelId:     "1",
		NativeLangCode: "zh-CN",
		TargetLangCode: "en-US",
	}

	err := suite.userModel.SaveOne(user)
	require.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), user.Id)
	assert.NotZero(suite.T(), user.CreatedAt)
	assert.NotZero(suite.T(), user.UpdatedAt)
}

// TestFindByUsername 测试根据用户名查找用户
func (suite *UserModelTestSuite) TestFindByUsername() {
	// 创建测试用户
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "findtest001")

	// 测试查找存在的用户
	foundUser, err := suite.userModel.FindByUsername("findtest001")
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), foundUser)
	assert.Equal(suite.T(), testUser.Id, foundUser.Id)
	assert.Equal(suite.T(), testUser.Username, foundUser.Username)

	// 测试查找不存在的用户
	notFoundUser, err := suite.userModel.FindByUsername("nonexistent")
	require.NoError(suite.T(), err)
	assert.Nil(suite.T(), notFoundUser)
}

// TestFindByAppleUserIdentifier 测试根据Apple用户标识查找用户
func (suite *UserModelTestSuite) TestFindByAppleUserIdentifier() {
	// 创建带Apple标识的测试用户
	user := &model.User{
		Username:            "appleuser001",
		PasswordDigest:      "$2a$10$test.hash.password",
		Nickname:            "Apple User 001",
		Status:              0,
		AppleUserIdentifier: "apple_test_id_001",
		VipLevelId:          "1",
	}
	err := suite.userModel.SaveOne(user)
	require.NoError(suite.T(), err)

	// 测试查找存在的Apple用户
	foundUser, err := suite.userModel.FindByAppleUserIdentifier("apple_test_id_001")
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), foundUser)
	assert.Equal(suite.T(), user.Id, foundUser.Id)
	assert.Equal(suite.T(), "apple_test_id_001", foundUser.AppleUserIdentifier)

	// 测试查找不存在的Apple用户
	notFoundUser, err := suite.userModel.FindByAppleUserIdentifier("nonexistent_apple_id")
	require.NoError(suite.T(), err)
	assert.Nil(suite.T(), notFoundUser)
}

// TestUpdateUserInfo 测试更新用户信息
func (suite *UserModelTestSuite) TestUpdateUserInfo() {
	// 创建测试用户
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "updatetest001")
	originalUpdatedAt := testUser.UpdatedAt

	// 等待一秒确保时间戳不同
	time.Sleep(time.Second)

	// 更新用户信息
	updateData := map[string]interface{}{
		"nickname":          "Updated Nickname",
		"avatar":           "https://test.com/new_avatar.jpg",
		"native_lang_code": "en-US",
		"target_lang_code": "zh-CN",
	}

	err := suite.userModel.UpdateUserInfo(testUser.Id, updateData)
	require.NoError(suite.T(), err)

	// 验证更新结果
	var updatedUser model.User
	err = suite.helper.DB.First(&updatedUser, "id = ?", testUser.Id).Error
	require.NoError(suite.T(), err)

	assert.Equal(suite.T(), "Updated Nickname", updatedUser.Nickname)
	assert.Equal(suite.T(), "https://test.com/new_avatar.jpg", updatedUser.Avatar)
	assert.Equal(suite.T(), "en-US", updatedUser.NativeLangCode)
	assert.Equal(suite.T(), "zh-CN", updatedUser.TargetLangCode)
	assert.True(suite.T(), updatedUser.UpdatedAt.After(originalUpdatedAt))
}

// TestDeleteUser 测试删除用户（软删除）
func (suite *UserModelTestSuite) TestDeleteUser() {
	// 创建测试用户
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "deletetest001")

	// 执行软删除
	err := suite.userModel.DeleteUser(testUser.Id)
	require.NoError(suite.T(), err)

	// 验证用户状态已更新为已注销
	var deletedUser model.User
	err = suite.helper.DB.First(&deletedUser, "id = ?", testUser.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 2, deletedUser.Status) // 2表示已注销

	// 验证创建了删除记录
	var deletedRecord model.DeletedUser
	err = suite.helper.DB.First(&deletedRecord, "uid = ?", testUser.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), testUser.Id, deletedRecord.Uid)
	assert.NotZero(suite.T(), deletedRecord.DeletedTimestamp)
}

// TestGetUserPlayerConfig 测试获取用户播放器配置
func (suite *UserModelTestSuite) TestGetUserPlayerConfig() {
	// 创建测试用户
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "configtest001")

	// 创建播放器配置
	config := suite.dbHelper.CreateTestUserPlayerConfig(suite.T(), testUser.Id)

	// 测试获取配置
	foundConfig, err := suite.userModel.GetUserPlayerConfig(testUser.Id)
	require.NoError(suite.T(), err)
	require.NotNil(suite.T(), foundConfig)
	assert.Equal(suite.T(), config.Uid, foundConfig.Uid)
	assert.Equal(suite.T(), config.PlaybackSpeed, foundConfig.PlaybackSpeed)
	assert.Equal(suite.T(), config.SubtitleLanguage, foundConfig.SubtitleLanguage)

	// 测试获取不存在的配置
	notFoundConfig, err := suite.userModel.GetUserPlayerConfig("nonexistent")
	require.NoError(suite.T(), err)
	assert.Nil(suite.T(), notFoundConfig)
}

// TestUpdateUserPlayerConfig 测试更新用户播放器配置
func (suite *UserModelTestSuite) TestUpdateUserPlayerConfig() {
	// 创建测试用户
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "updateconfigtest001")

	// 创建初始配置
	config := suite.dbHelper.CreateTestUserPlayerConfig(suite.T(), testUser.Id)

	// 更新配置
	updateData := &model.UserPlayerConfig{
		Uid:              testUser.Id,
		PlaybackSpeed:    1.5,
		SubtitleLanguage: "zh",
		AutoPlay:         false,
		SubtitleSize:     20,
		VolumeLevel:      90,
	}

	err := suite.userModel.UpdateUserPlayerConfig(updateData)
	require.NoError(suite.T(), err)

	// 验证更新结果
	var updatedConfig model.UserPlayerConfig
	err = suite.helper.DB.First(&updatedConfig, "uid = ?", testUser.Id).Error
	require.NoError(suite.T(), err)

	assert.Equal(suite.T(), 1.5, updatedConfig.PlaybackSpeed)
	assert.Equal(suite.T(), "zh", updatedConfig.SubtitleLanguage)
	assert.False(suite.T(), updatedConfig.AutoPlay)
	assert.Equal(suite.T(), 20, updatedConfig.SubtitleSize)
	assert.Equal(suite.T(), 90, updatedConfig.VolumeLevel)
}

// TestAddWatchHistory 测试添加观看历史
func (suite *UserModelTestSuite) TestAddWatchHistory() {
	// 创建测试用户
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "watchtest001")

	// 添加观看历史
	history := &model.WatchHistory{
		Uid:            testUser.Id,
		ResourceId:     "resource_001",
		ResourceType:   1,
		Position:       120,
		Duration:       300,
		WatchTimestamp: time.Now().UnixMilli(),
	}

	err := suite.userModel.AddWatchHistory(history)
	require.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), history.Id)

	// 验证历史记录已保存
	var savedHistory model.WatchHistory
	err = suite.helper.DB.First(&savedHistory, "id = ?", history.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), testUser.Id, savedHistory.Uid)
	assert.Equal(suite.T(), "resource_001", savedHistory.ResourceId)
	assert.Equal(suite.T(), 120, savedHistory.Position)
}

// TestGetWatchHistory 测试获取观看历史
func (suite *UserModelTestSuite) TestGetWatchHistory() {
	// 创建测试用户
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "gethistorytest001")

	// 创建多条观看历史
	histories := []*model.WatchHistory{
		{
			Uid:            testUser.Id,
			ResourceId:     "resource_001",
			ResourceType:   1,
			Position:       120,
			Duration:       300,
			WatchTimestamp: time.Now().UnixMilli(),
		},
		{
			Uid:            testUser.Id,
			ResourceId:     "resource_002",
			ResourceType:   1,
			Position:       60,
			Duration:       240,
			WatchTimestamp: time.Now().UnixMilli() - 3600000, // 1小时前
		},
	}

	for _, history := range histories {
		err := suite.userModel.AddWatchHistory(history)
		require.NoError(suite.T(), err)
	}

	// 获取观看历史
	foundHistories, err := suite.userModel.GetWatchHistory(testUser.Id, 10, 0)
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), foundHistories, 2)

	// 验证按时间倒序排列
	assert.True(suite.T(), foundHistories[0].WatchTimestamp >= foundHistories[1].WatchTimestamp)
}

// TestDeleteWatchHistory 测试删除观看历史
func (suite *UserModelTestSuite) TestDeleteWatchHistory() {
	// 创建测试用户
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "deletehistorytest001")

	// 创建观看历史
	history := &model.WatchHistory{
		Uid:            testUser.Id,
		ResourceId:     "resource_001",
		ResourceType:   1,
		Position:       120,
		Duration:       300,
		WatchTimestamp: time.Now().UnixMilli(),
	}
	err := suite.userModel.AddWatchHistory(history)
	require.NoError(suite.T(), err)

	// 删除观看历史
	err = suite.userModel.DeleteWatchHistory(testUser.Id, []string{history.Id})
	require.NoError(suite.T(), err)

	// 验证历史记录已删除
	var count int64
	suite.helper.DB.Model(&model.WatchHistory{}).Where("id = ?", history.Id).Count(&count)
	assert.Equal(suite.T(), int64(0), count)
}

// 运行测试套件
func TestUserModelSuite(t *testing.T) {
	suite.Run(t, new(UserModelTestSuite))
}
