package benefit

import (
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/request"
	"loop/pkg/constants"
	"loop/pkg/web"
	"loop/test/utils"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// BenefitRepoTestSuite 权益数据层测试套件
type BenefitRepoTestSuite struct {
	suite.Suite
	helper      *utils.TestHelper
	dbHelper    *utils.DBHelper
	benefitRepo *data.BenefitRepo
	ctx         *gin.Context
}

// SetupSuite 设置测试套件
func (suite *BenefitRepoTestSuite) SetupSuite() {
	suite.helper = utils.NewTestHelper(suite.T())
	suite.dbHelper = utils.NewDBHelper(suite.helper.DB)

	// 创建测试上下文
	suite.ctx, _ = gin.CreateTestContext(nil)

	// 创建权益仓库实例
	dbModel := &model.DbModel{DBExtension: *suite.helper.DB}
	benefitModel := model.NewBenefitModel(dbModel, suite.helper.Config)
	vipModel := model.NewVipModel(dbModel, suite.helper.Config)
	suite.benefitRepo = data.NewBenefitRepo(benefitModel, vipModel)
}

// TearDownSuite 清理测试套件
func (suite *BenefitRepoTestSuite) TearDownSuite() {
	suite.helper.CleanupDB(suite.T())
}

// SetupTest 每个测试前的设置
func (suite *BenefitRepoTestSuite) SetupTest() {
	// 清理测试数据
	tables := []string{
		"user_benefit_logs", "user_benefits", "vip_benefits",
		"benefits", "benefit_groups", "user_vip_relations",
	}
	suite.dbHelper.CleanupTestData(suite.T(), tables...)
}

// TestGetUserBenefits 测试获取用户权益
func (suite *BenefitRepoTestSuite) TestGetUserBenefits() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "benefitrepouser001")
	vip := suite.dbHelper.CreateTestVIP(suite.T(), "测试Pro", 100)
	
	// 创建VIP关系
	suite.dbHelper.CreateTestUserVIPRelation(suite.T(), testUser.Id, vip.Id, 30)

	// 创建权益组和权益
	group1 := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	group2 := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "AI_CALL_LIMIT", "AI调用限制")
	benefit1 := suite.dbHelper.CreateTestBenefit(suite.T(), group1.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)
	benefit2 := suite.dbHelper.CreateTestBenefit(suite.T(), group2.Id, "DAILY_AI_CALL_LIMIT", "每日AI调用限制", 1, 10)

	// 创建VIP权益关联
	suite.dbHelper.CreateTestVipBenefit(suite.T(), vip.Id, uint(vip.Level), group1.Id, benefit1.Id, string(benefit1.BenefitCode))
	suite.dbHelper.CreateTestVipBenefit(suite.T(), vip.Id, uint(vip.Level), group2.Id, benefit2.Id, string(benefit2.BenefitCode))

	// 创建用户权益
	suite.dbHelper.CreateTestUserBenefit(suite.T(), testUser.Id, benefit1.Id, 3, 5)
	suite.dbHelper.CreateTestUserBenefit(suite.T(), testUser.Id, benefit2.Id, 8, 10)

	// 设置JWT上下文
	suite.ctx.Set("uid", testUser.Id)

	result := suite.benefitRepo.GetUserBenefits(suite.ctx)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证返回的权益数据
	benefits, ok := result.Data.([]interface{})
	require.True(suite.T(), ok)
	assert.Len(suite.T(), benefits, 2)
}

// TestGetUserBenefitsByGroup 测试根据权益组获取用户权益
func (suite *BenefitRepoTestSuite) TestGetUserBenefitsByGroup() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "benefitrepouser002")
	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	benefit := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)
	suite.dbHelper.CreateTestUserBenefit(suite.T(), testUser.Id, benefit.Id, 3, 5)

	// 设置JWT上下文
	suite.ctx.Set("uid", testUser.Id)

	req := request.GetUserBenefitsByGroupReq{
		GroupCode: string(group.GroupCode),
	}

	result := suite.benefitRepo.GetUserBenefitsByGroup(suite.ctx, req)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证返回的权益数据
	benefits, ok := result.Data.([]interface{})
	require.True(suite.T(), ok)
	assert.Len(suite.T(), benefits, 1)
}

// TestConsumeBenefit 测试消耗权益
func (suite *BenefitRepoTestSuite) TestConsumeBenefit() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "benefitrepouser003")
	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	benefit := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)
	userBenefit := suite.dbHelper.CreateTestUserBenefit(suite.T(), testUser.Id, benefit.Id, 5, 5)

	// 设置JWT上下文
	suite.ctx.Set("uid", testUser.Id)

	req := request.ConsumeBenefitReq{
		BenefitCode: string(benefit.BenefitCode),
		Amount:      1,
		Reason:      "观看视频消耗",
	}

	result := suite.benefitRepo.ConsumeBenefit(suite.ctx, req)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证权益已消耗
	var updatedUserBenefit model.UserBenefit
	err := suite.helper.DB.First(&updatedUserBenefit, "id = ?", userBenefit.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 4, updatedUserBenefit.CurrentValue)

	// 验证消耗日志已创建
	var log model.UserBenefitLog
	err = suite.helper.DB.First(&log, "uid = ? AND benefit_id = ? AND operation_type = ?", testUser.Id, benefit.Id, 1).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), -1, log.ChangeAmount)
	assert.Equal(suite.T(), 5, log.BeforeValue)
	assert.Equal(suite.T(), 4, log.AfterValue)
	assert.Equal(suite.T(), "观看视频消耗", log.Reason)
}

// TestConsumeBenefitInsufficientBalance 测试消耗权益余额不足
func (suite *BenefitRepoTestSuite) TestConsumeBenefitInsufficientBalance() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "benefitrepouser004")
	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	benefit := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)
	suite.dbHelper.CreateTestUserBenefit(suite.T(), testUser.Id, benefit.Id, 1, 5) // 只有1个余额

	// 设置JWT上下文
	suite.ctx.Set("uid", testUser.Id)

	req := request.ConsumeBenefitReq{
		BenefitCode: string(benefit.BenefitCode),
		Amount:      2, // 尝试消耗2个，但只有1个
		Reason:      "观看视频消耗",
	}

	result := suite.benefitRepo.ConsumeBenefit(suite.ctx, req)
	require.NotNil(suite.T(), result)
	assert.NotEqual(suite.T(), web.SUCCESS, result.Code)
	assert.Contains(suite.T(), result.Msg, "余额不足") // 根据实际错误消息调整
}

// TestAllocateBenefit 测试分配权益
func (suite *BenefitRepoTestSuite) TestAllocateBenefit() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "benefitrepouser005")
	vip := suite.dbHelper.CreateTestVIP(suite.T(), "测试Pro", 100)
	suite.dbHelper.CreateTestUserVIPRelation(suite.T(), testUser.Id, vip.Id, 30)

	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	benefit := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)
	suite.dbHelper.CreateTestVipBenefit(suite.T(), vip.Id, uint(vip.Level), group.Id, benefit.Id, string(benefit.BenefitCode))

	// 设置JWT上下文
	suite.ctx.Set("uid", testUser.Id)

	req := request.AllocateBenefitReq{
		BenefitCode: string(benefit.BenefitCode),
		Amount:      10, // 分配10个权益
		Reason:      "VIP权益分配",
	}

	result := suite.benefitRepo.AllocateBenefit(suite.ctx, req)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证用户权益已创建或更新
	var userBenefit model.UserBenefit
	err := suite.helper.DB.First(&userBenefit, "uid = ? AND benefit_id = ?", testUser.Id, benefit.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 10, userBenefit.CurrentValue)
	assert.Equal(suite.T(), 10, userBenefit.MaxValue)

	// 验证分配日志已创建
	var log model.UserBenefitLog
	err = suite.helper.DB.First(&log, "uid = ? AND benefit_id = ? AND operation_type = ?", testUser.Id, benefit.Id, 2).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 10, log.ChangeAmount)
	assert.Equal(suite.T(), 0, log.BeforeValue)
	assert.Equal(suite.T(), 10, log.AfterValue)
	assert.Equal(suite.T(), "VIP权益分配", log.Reason)
}

// TestCheckBenefitAvailability 测试检查权益可用性
func (suite *BenefitRepoTestSuite) TestCheckBenefitAvailability() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "benefitrepouser006")
	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	benefit := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)
	suite.dbHelper.CreateTestUserBenefit(suite.T(), testUser.Id, benefit.Id, 3, 5)

	// 设置JWT上下文
	suite.ctx.Set("uid", testUser.Id)

	req := request.CheckBenefitAvailabilityReq{
		BenefitCode: string(benefit.BenefitCode),
		Amount:      2, // 检查是否有2个可用
	}

	result := suite.benefitRepo.CheckBenefitAvailability(suite.ctx, req)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证返回的可用性信息
	data, ok := result.Data.(map[string]interface{})
	require.True(suite.T(), ok)
	assert.Equal(suite.T(), true, data["available"])
	assert.Equal(suite.T(), float64(3), data["current_value"])
	assert.Equal(suite.T(), float64(5), data["max_value"])
}

// TestCheckBenefitAvailabilityInsufficient 测试检查权益可用性不足
func (suite *BenefitRepoTestSuite) TestCheckBenefitAvailabilityInsufficient() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "benefitrepouser007")
	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	benefit := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)
	suite.dbHelper.CreateTestUserBenefit(suite.T(), testUser.Id, benefit.Id, 1, 5) // 只有1个

	// 设置JWT上下文
	suite.ctx.Set("uid", testUser.Id)

	req := request.CheckBenefitAvailabilityReq{
		BenefitCode: string(benefit.BenefitCode),
		Amount:      3, // 检查是否有3个可用，但只有1个
	}

	result := suite.benefitRepo.CheckBenefitAvailability(suite.ctx, req)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证返回的可用性信息
	data, ok := result.Data.(map[string]interface{})
	require.True(suite.T(), ok)
	assert.Equal(suite.T(), false, data["available"])
	assert.Equal(suite.T(), float64(1), data["current_value"])
	assert.Equal(suite.T(), float64(5), data["max_value"])
}

// TestGetUserBenefitLogs 测试获取用户权益日志
func (suite *BenefitRepoTestSuite) TestGetUserBenefitLogs() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "benefitrepouser008")
	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	benefit := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)

	// 创建权益日志
	logs := []*model.UserBenefitLog{
		{
			Uid:             testUser.Id,
			BenefitID:       benefit.Id,
			OperationType:   1, // 消耗
			ChangeAmount:    -1,
			BeforeValue:     5,
			AfterValue:      4,
			Reason:          "观看视频消耗",
			CreateTimestamp: 1703002600000,
		},
		{
			Uid:             testUser.Id,
			BenefitID:       benefit.Id,
			OperationType:   2, // 分配
			ChangeAmount:    5,
			BeforeValue:     0,
			AfterValue:      5,
			Reason:          "每日权益分配",
			CreateTimestamp: 1703001600000,
		},
	}

	for _, log := range logs {
		err := suite.helper.DB.Create(log).Error
		require.NoError(suite.T(), err)
	}

	// 设置JWT上下文
	suite.ctx.Set("uid", testUser.Id)

	result := suite.benefitRepo.GetUserBenefitLogs(suite.ctx)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证返回的日志数据
	logData, ok := result.Data.([]interface{})
	require.True(suite.T(), ok)
	assert.Len(suite.T(), logData, 2)
}

// TestResetUserBenefits 测试重置用户权益
func (suite *BenefitRepoTestSuite) TestResetUserBenefits() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "benefitrepouser009")
	vip := suite.dbHelper.CreateTestVIP(suite.T(), "测试Pro", 100)
	suite.dbHelper.CreateTestUserVIPRelation(suite.T(), testUser.Id, vip.Id, 30)

	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	benefit := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)
	suite.dbHelper.CreateTestVipBenefit(suite.T(), vip.Id, uint(vip.Level), group.Id, benefit.Id, string(benefit.BenefitCode))
	
	// 创建已消耗的用户权益
	userBenefit := suite.dbHelper.CreateTestUserBenefit(suite.T(), testUser.Id, benefit.Id, 2, 5) // 已消耗3个

	// 设置JWT上下文
	suite.ctx.Set("uid", testUser.Id)

	req := request.ResetUserBenefitsReq{
		GroupCode: string(group.GroupCode),
		Reason:    "每日重置",
	}

	result := suite.benefitRepo.ResetUserBenefits(suite.ctx, req)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证权益已重置
	var updatedUserBenefit model.UserBenefit
	err := suite.helper.DB.First(&updatedUserBenefit, "id = ?", userBenefit.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 5, updatedUserBenefit.CurrentValue) // 重置为最大值
	assert.Equal(suite.T(), 5, updatedUserBenefit.MaxValue)

	// 验证重置日志已创建
	var log model.UserBenefitLog
	err = suite.helper.DB.First(&log, "uid = ? AND benefit_id = ? AND operation_type = ?", testUser.Id, benefit.Id, 3).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 3, log.ChangeAmount) // 增加了3个
	assert.Equal(suite.T(), 2, log.BeforeValue)
	assert.Equal(suite.T(), 5, log.AfterValue)
	assert.Equal(suite.T(), "每日重置", log.Reason)
}

// 运行测试套件
func TestBenefitRepoSuite(t *testing.T) {
	suite.Run(t, new(BenefitRepoTestSuite))
}
