package benefit

import (
	"bytes"
	"encoding/json"
	"loop/internal/api"
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/request"
	"loop/test/utils"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// BenefitApiTestSuite 权益API测试套件
type BenefitApiTestSuite struct {
	suite.Suite
	helper       *utils.TestHelper
	dbHelper     *utils.DBHelper
	assertHelper *utils.AssertHelper
	benefitApi   *api.BenefitApi
	router       *gin.Engine
}

// SetupSuite 设置测试套件
func (suite *BenefitApiTestSuite) SetupSuite() {
	suite.helper = utils.NewTestHelper(suite.T())
	suite.dbHelper = utils.NewDBHelper(suite.helper.DB)
	suite.assertHelper = utils.NewAssertHelper(suite.T())

	// 创建API实例
	dbModel := &model.DbModel{DBExtension: *suite.helper.DB}
	benefitModel := model.NewBenefitModel(dbModel, suite.helper.Config)
	vipModel := model.NewVipModel(dbModel, suite.helper.Config)
	benefitRepo := data.NewBenefitRepo(benefitModel, vipModel)
	suite.benefitApi = api.NewBenefitApi(benefitRepo, dbModel)

	// 设置路由
	suite.router = gin.New()
	suite.setupRoutes()
}

// setupRoutes 设置测试路由
func (suite *BenefitApiTestSuite) setupRoutes() {
	v1 := suite.router.Group("/api/v1")

	// 需要认证的路由
	protected := v1.Group("/")
	protected.Use(suite.mockJWTMiddleware())
	{
		protected.GET("/benefits", suite.benefitApi.GetUserBenefits)
		protected.GET("/benefits/group/:groupCode", suite.benefitApi.GetUserBenefitsByGroup)
		protected.POST("/benefits/consume", suite.benefitApi.ConsumeBenefit)
		protected.POST("/benefits/check", suite.benefitApi.CheckBenefitAvailability)
		protected.GET("/benefits/logs", suite.benefitApi.GetUserBenefitLogs)
	}

	// 管理员路由
	admin := v1.Group("/admin")
	admin.Use(suite.mockAdminMiddleware())
	{
		admin.POST("/benefits/allocate", suite.benefitApi.AllocateBenefit)
		admin.POST("/benefits/reset", suite.benefitApi.ResetUserBenefits)
	}
}

// mockJWTMiddleware 模拟JWT中间件
func (suite *BenefitApiTestSuite) mockJWTMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.GetHeader("X-Test-UID")
		if uid != "" {
			c.Set("uid", uid)
			c.Set("username", "testuser")
			c.Set("status", 0)
		}
		c.Next()
	}
}

// mockAdminMiddleware 模拟管理员中间件
func (suite *BenefitApiTestSuite) mockAdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("admin", true)
		c.Next()
	}
}

// TearDownSuite 清理测试套件
func (suite *BenefitApiTestSuite) TearDownSuite() {
	suite.helper.CleanupDB(suite.T())
}

// SetupTest 每个测试前的设置
func (suite *BenefitApiTestSuite) SetupTest() {
	// 清理测试数据
	tables := []string{
		"user_benefit_logs", "user_benefits", "vip_benefits",
		"benefits", "benefit_groups", "user_vip_relations",
	}
	suite.dbHelper.CleanupTestData(suite.T(), tables...)
}

// TestGetUserBenefitsAPI 测试获取用户权益API
func (suite *BenefitApiTestSuite) TestGetUserBenefitsAPI() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "benefitapiuser001")
	vip := suite.dbHelper.CreateTestVIP(suite.T(), "测试Pro", 100)
	suite.dbHelper.CreateTestUserVIPRelation(suite.T(), testUser.Id, vip.Id, 30)

	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	benefit := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)
	suite.dbHelper.CreateTestVipBenefit(suite.T(), vip.Id, uint(vip.Level), group.Id, benefit.Id, string(benefit.BenefitCode))
	suite.dbHelper.CreateTestUserBenefit(suite.T(), testUser.Id, benefit.Id, 3, 5)

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/benefits", nil)
	req.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, req)

	suite.assertHelper.AssertSuccessResponse(w, nil)
	suite.assertHelper.AssertListResponse(w, 1)
}

// TestGetUserBenefitsByGroupAPI 测试根据权益组获取用户权益API
func (suite *BenefitApiTestSuite) TestGetUserBenefitsByGroupAPI() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "benefitapiuser002")
	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	benefit := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)
	suite.dbHelper.CreateTestUserBenefit(suite.T(), testUser.Id, benefit.Id, 3, 5)

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/benefits/group/VIDEO_LIMIT", nil)
	req.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, req)

	suite.assertHelper.AssertSuccessResponse(w, nil)
	suite.assertHelper.AssertListResponse(w, 1)
}

// TestConsumeBenefitAPI 测试消耗权益API
func (suite *BenefitApiTestSuite) TestConsumeBenefitAPI() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "benefitapiuser003")
	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	benefit := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)
	userBenefit := suite.dbHelper.CreateTestUserBenefit(suite.T(), testUser.Id, benefit.Id, 5, 5)

	reqData := request.ConsumeBenefitReq{
		BenefitCode: string(benefit.BenefitCode),
		Amount:      1,
		Reason:      "观看视频消耗",
	}

	jsonData, _ := json.Marshal(reqData)
	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("POST", "/api/v1/benefits/consume", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证权益已消耗
	var updatedUserBenefit model.UserBenefit
	err := suite.helper.DB.First(&updatedUserBenefit, "id = ?", userBenefit.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 4, updatedUserBenefit.CurrentValue)

	// 验证消耗日志已创建
	var log model.UserBenefitLog
	err = suite.helper.DB.First(&log, "uid = ? AND benefit_id = ? AND operation_type = ?", testUser.Id, benefit.Id, 1).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), -1, log.ChangeAmount)
}

// TestConsumeBenefitAPIInsufficientBalance 测试消耗权益余额不足API
func (suite *BenefitApiTestSuite) TestConsumeBenefitAPIInsufficientBalance() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "benefitapiuser004")
	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	benefit := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)
	suite.dbHelper.CreateTestUserBenefit(suite.T(), testUser.Id, benefit.Id, 1, 5) // 只有1个余额

	reqData := request.ConsumeBenefitReq{
		BenefitCode: string(benefit.BenefitCode),
		Amount:      2, // 尝试消耗2个
		Reason:      "观看视频消耗",
	}

	jsonData, _ := json.Marshal(reqData)
	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("POST", "/api/v1/benefits/consume", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertErrorResponse(w, 400, "")
}

// TestCheckBenefitAvailabilityAPI 测试检查权益可用性API
func (suite *BenefitApiTestSuite) TestCheckBenefitAvailabilityAPI() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "benefitapiuser005")
	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	benefit := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)
	suite.dbHelper.CreateTestUserBenefit(suite.T(), testUser.Id, benefit.Id, 3, 5)

	reqData := request.CheckBenefitAvailabilityReq{
		BenefitCode: string(benefit.BenefitCode),
		Amount:      2,
	}

	jsonData, _ := json.Marshal(reqData)
	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("POST", "/api/v1/benefits/check", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证返回的可用性信息
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)

	data, ok := response["data"].(map[string]interface{})
	require.True(suite.T(), ok)
	assert.Equal(suite.T(), true, data["available"])
	assert.Equal(suite.T(), float64(3), data["current_value"])
}

// TestAllocateBenefitAPI 测试分配权益API
func (suite *BenefitApiTestSuite) TestAllocateBenefitAPI() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "benefitapiuser006")
	vip := suite.dbHelper.CreateTestVIP(suite.T(), "测试Pro", 100)
	suite.dbHelper.CreateTestUserVIPRelation(suite.T(), testUser.Id, vip.Id, 30)

	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	benefit := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)
	suite.dbHelper.CreateTestVipBenefit(suite.T(), vip.Id, uint(vip.Level), group.Id, benefit.Id, string(benefit.BenefitCode))

	reqData := request.AllocateBenefitReq{
		Uid:         testUser.Id,
		BenefitCode: string(benefit.BenefitCode),
		Amount:      10,
		Reason:      "VIP权益分配",
	}

	jsonData, _ := json.Marshal(reqData)
	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("POST", "/api/v1/admin/benefits/allocate", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证用户权益已创建
	var userBenefit model.UserBenefit
	err := suite.helper.DB.First(&userBenefit, "uid = ? AND benefit_id = ?", testUser.Id, benefit.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 10, userBenefit.CurrentValue)
	assert.Equal(suite.T(), 10, userBenefit.MaxValue)

	// 验证分配日志已创建
	var log model.UserBenefitLog
	err = suite.helper.DB.First(&log, "uid = ? AND benefit_id = ? AND operation_type = ?", testUser.Id, benefit.Id, 2).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 10, log.ChangeAmount)
}

// TestGetUserBenefitLogsAPI 测试获取用户权益日志API
func (suite *BenefitApiTestSuite) TestGetUserBenefitLogsAPI() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "benefitapiuser007")
	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	benefit := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)

	// 创建权益日志
	log := &model.UserBenefitLog{
		Uid:             testUser.Id,
		BenefitID:       benefit.Id,
		OperationType:   1, // 消耗
		ChangeAmount:    -1,
		BeforeValue:     5,
		AfterValue:      4,
		Reason:          "观看视频消耗",
		CreateTimestamp: 1703001600000,
	}
	err := suite.helper.DB.Create(log).Error
	require.NoError(suite.T(), err)

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/benefits/logs", nil)
	req.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, req)

	suite.assertHelper.AssertSuccessResponse(w, nil)
	suite.assertHelper.AssertListResponse(w, 1)
}

// TestResetUserBenefitsAPI 测试重置用户权益API
func (suite *BenefitApiTestSuite) TestResetUserBenefitsAPI() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "benefitapiuser008")
	vip := suite.dbHelper.CreateTestVIP(suite.T(), "测试Pro", 100)
	suite.dbHelper.CreateTestUserVIPRelation(suite.T(), testUser.Id, vip.Id, 30)

	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	benefit := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)
	suite.dbHelper.CreateTestVipBenefit(suite.T(), vip.Id, uint(vip.Level), group.Id, benefit.Id, string(benefit.BenefitCode))
	userBenefit := suite.dbHelper.CreateTestUserBenefit(suite.T(), testUser.Id, benefit.Id, 2, 5) // 已消耗3个

	reqData := request.ResetUserBenefitsReq{
		Uid:       testUser.Id,
		GroupCode: string(group.GroupCode),
		Reason:    "每日重置",
	}

	jsonData, _ := json.Marshal(reqData)
	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("POST", "/api/v1/admin/benefits/reset", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证权益已重置
	var updatedUserBenefit model.UserBenefit
	err := suite.helper.DB.First(&updatedUserBenefit, "id = ?", userBenefit.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 5, updatedUserBenefit.CurrentValue) // 重置为最大值

	// 验证重置日志已创建
	var log model.UserBenefitLog
	err = suite.helper.DB.First(&log, "uid = ? AND benefit_id = ? AND operation_type = ?", testUser.Id, benefit.Id, 3).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 3, log.ChangeAmount) // 增加了3个
}

// TestUnauthorizedAccess 测试未授权访问
func (suite *BenefitApiTestSuite) TestUnauthorizedAccess() {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/benefits", nil)
	// 不设置X-Test-UID头部

	suite.router.ServeHTTP(w, req)

	// 根据实际的中间件实现调整期望的状态码
	assert.NotEqual(suite.T(), 200, w.Code)
}

// 运行测试套件
func TestBenefitApiSuite(t *testing.T) {
	suite.Run(t, new(BenefitApiTestSuite))
}
