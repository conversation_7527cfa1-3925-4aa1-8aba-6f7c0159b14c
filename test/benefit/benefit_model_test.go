package benefit

import (
	"loop/internal/model"
	"loop/pkg/constants"
	"loop/test/utils"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// BenefitModelTestSuite 权益模型测试套件
type BenefitModelTestSuite struct {
	suite.Suite
	helper       *utils.TestHelper
	dbHelper     *utils.DBHelper
	benefitModel *model.BenefitModel
	ctx          *gin.Context
}

// SetupSuite 设置测试套件
func (suite *BenefitModelTestSuite) SetupSuite() {
	suite.helper = utils.NewTestHelper(suite.T())
	suite.dbHelper = utils.NewDBHelper(suite.helper.DB)

	// 创建测试上下文
	suite.ctx, _ = gin.CreateTestContext(nil)

	// 创建权益模型实例
	dbModel := &model.DbModel{DBExtension: *suite.helper.DB}
	suite.benefitModel = model.NewBenefitModel(dbModel, suite.helper.Config)
}

// TearDownSuite 清理测试套件
func (suite *BenefitModelTestSuite) TearDownSuite() {
	suite.helper.CleanupDB(suite.T())
}

// SetupTest 每个测试前的设置
func (suite *BenefitModelTestSuite) SetupTest() {
	// 清理测试数据
	tables := []string{
		"user_benefit_logs", "user_benefits", "vip_benefits",
		"benefits", "benefit_groups",
	}
	suite.dbHelper.CleanupTestData(suite.T(), tables...)
}

// TestCreateBenefitGroup 测试创建权益组
func (suite *BenefitModelTestSuite) TestCreateBenefitGroup() {
	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")

	assert.NotEmpty(suite.T(), group.Id)
	assert.Equal(suite.T(), constants.BenefitGroupCode("VIDEO_LIMIT"), group.GroupCode)
	assert.Equal(suite.T(), "视频观看限制", group.GroupName)
	assert.Equal(suite.T(), 1, group.Status) // 启用状态
}

// TestCreateBenefit 测试创建权益
func (suite *BenefitModelTestSuite) TestCreateBenefit() {
	// 先创建权益组
	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")

	// 创建权益
	benefit := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)

	assert.NotEmpty(suite.T(), benefit.Id)
	assert.Equal(suite.T(), group.Id, benefit.BenefitGroupID)
	assert.Equal(suite.T(), constants.BenefitCode("DAILY_VIDEO_LIMIT"), benefit.BenefitCode)
	assert.Equal(suite.T(), "每日视频观看限制", benefit.BenefitName)
	assert.Equal(suite.T(), 1, benefit.BenefitType) // 数量类型
	assert.Equal(suite.T(), 5, benefit.DefaultValue)
}

// TestGetBenefitsByGroup 测试根据权益组获取权益
func (suite *BenefitModelTestSuite) TestGetBenefitsByGroup() {
	// 创建权益组和权益
	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	benefit1 := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)
	benefit2 := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "WEEKLY_VIDEO_LIMIT", "每周视频观看限制", 1, 35)

	// 获取权益列表
	var benefits []model.Benefit
	err := suite.benefitModel.GetList(&benefits, model.Benefit{BenefitGroupID: group.Id})
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), benefits, 2)

	// 验证权益数据
	benefitCodes := make([]constants.BenefitCode, len(benefits))
	for i, b := range benefits {
		benefitCodes[i] = b.BenefitCode
	}
	assert.Contains(suite.T(), benefitCodes, benefit1.BenefitCode)
	assert.Contains(suite.T(), benefitCodes, benefit2.BenefitCode)
}

// TestCreateVipBenefit 测试创建VIP权益关联
func (suite *BenefitModelTestSuite) TestCreateVipBenefit() {
	// 创建测试数据
	vip := suite.dbHelper.CreateTestVIP(suite.T(), "测试Pro", 100)
	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	benefit := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)

	// 创建VIP权益关联
	vipBenefit := suite.dbHelper.CreateTestVipBenefit(suite.T(), vip.Id, uint(vip.Level), group.Id, benefit.Id, string(benefit.BenefitCode))

	assert.NotEmpty(suite.T(), vipBenefit.Id)
	assert.Equal(suite.T(), vip.Id, vipBenefit.VipID)
	assert.Equal(suite.T(), uint(vip.Level), vipBenefit.VipLevel)
	assert.Equal(suite.T(), group.Id, vipBenefit.BenefitGroupID)
	assert.Equal(suite.T(), benefit.Id, vipBenefit.BenefitID)
}

// TestGetVipBenefits 测试获取VIP权益
func (suite *BenefitModelTestSuite) TestGetVipBenefits() {
	// 创建测试数据
	vip := suite.dbHelper.CreateTestVIP(suite.T(), "测试Pro", 100)
	group1 := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	group2 := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "AI_CALL_LIMIT", "AI调用限制")
	benefit1 := suite.dbHelper.CreateTestBenefit(suite.T(), group1.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)
	benefit2 := suite.dbHelper.CreateTestBenefit(suite.T(), group2.Id, "DAILY_AI_CALL_LIMIT", "每日AI调用限制", 1, 10)

	// 创建VIP权益关联
	suite.dbHelper.CreateTestVipBenefit(suite.T(), vip.Id, uint(vip.Level), group1.Id, benefit1.Id, string(benefit1.BenefitCode))
	suite.dbHelper.CreateTestVipBenefit(suite.T(), vip.Id, uint(vip.Level), group2.Id, benefit2.Id, string(benefit2.BenefitCode))

	// 获取VIP权益
	var vipBenefits []model.VipBenefit
	err := suite.benefitModel.GetList(&vipBenefits, model.VipBenefit{VipID: vip.Id})
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), vipBenefits, 2)

	// 验证权益数据
	benefitCodes := make([]constants.BenefitCode, len(vipBenefits))
	for i, vb := range vipBenefits {
		benefitCodes[i] = vb.BenefitCode
	}
	assert.Contains(suite.T(), benefitCodes, benefit1.BenefitCode)
	assert.Contains(suite.T(), benefitCodes, benefit2.BenefitCode)
}

// TestCreateUserBenefit 测试创建用户权益
func (suite *BenefitModelTestSuite) TestCreateUserBenefit() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "benefituser001")
	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	benefit := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)

	// 创建用户权益
	userBenefit := suite.dbHelper.CreateTestUserBenefit(suite.T(), testUser.Id, benefit.Id, 3, 5)

	assert.NotEmpty(suite.T(), userBenefit.Id)
	assert.Equal(suite.T(), testUser.Id, userBenefit.Uid)
	assert.Equal(suite.T(), benefit.Id, userBenefit.BenefitID)
	assert.Equal(suite.T(), 3, userBenefit.CurrentValue)
	assert.Equal(suite.T(), 5, userBenefit.MaxValue)
}

// TestGetUserBenefits 测试获取用户权益
func (suite *BenefitModelTestSuite) TestGetUserBenefits() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "benefituser002")
	group1 := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	group2 := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "AI_CALL_LIMIT", "AI调用限制")
	benefit1 := suite.dbHelper.CreateTestBenefit(suite.T(), group1.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)
	benefit2 := suite.dbHelper.CreateTestBenefit(suite.T(), group2.Id, "DAILY_AI_CALL_LIMIT", "每日AI调用限制", 1, 10)

	// 创建用户权益
	suite.dbHelper.CreateTestUserBenefit(suite.T(), testUser.Id, benefit1.Id, 3, 5)
	suite.dbHelper.CreateTestUserBenefit(suite.T(), testUser.Id, benefit2.Id, 8, 10)

	// 获取用户权益
	var userBenefits []model.UserBenefit
	err := suite.benefitModel.GetList(&userBenefits, model.UserBenefit{Uid: testUser.Id})
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), userBenefits, 2)

	// 验证权益数据
	benefitIDs := make([]uint, len(userBenefits))
	for i, ub := range userBenefits {
		benefitIDs[i] = ub.BenefitID
	}
	assert.Contains(suite.T(), benefitIDs, benefit1.Id)
	assert.Contains(suite.T(), benefitIDs, benefit2.Id)
}

// TestUpdateUserBenefit 测试更新用户权益
func (suite *BenefitModelTestSuite) TestUpdateUserBenefit() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "benefituser003")
	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	benefit := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)
	userBenefit := suite.dbHelper.CreateTestUserBenefit(suite.T(), testUser.Id, benefit.Id, 5, 5)

	// 更新用户权益（消耗1个）
	userBenefit.CurrentValue = 4
	err := suite.benefitModel.Update(userBenefit, "id = ?", userBenefit.Id)
	require.NoError(suite.T(), err)

	// 验证更新结果
	var updatedUserBenefit model.UserBenefit
	err = suite.helper.DB.First(&updatedUserBenefit, "id = ?", userBenefit.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 4, updatedUserBenefit.CurrentValue)
}

// TestCreateUserBenefitLog 测试创建用户权益日志
func (suite *BenefitModelTestSuite) TestCreateUserBenefitLog() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "benefituser004")
	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	benefit := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)

	// 创建权益日志
	log := &model.UserBenefitLog{
		Uid:           testUser.Id,
		BenefitID:     benefit.Id,
		OperationType: 1, // 消耗
		ChangeAmount:  -1,
		BeforeValue:   5,
		AfterValue:    4,
		Reason:        "观看视频消耗",
		CreateTime:    time.Now(),
		CreateTimestamp: time.Now().UnixMilli(),
	}

	err := suite.benefitModel.SaveOne(log)
	require.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), log.Id)

	// 验证日志已保存
	var savedLog model.UserBenefitLog
	err = suite.helper.DB.First(&savedLog, "id = ?", log.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), testUser.Id, savedLog.Uid)
	assert.Equal(suite.T(), benefit.Id, savedLog.BenefitID)
	assert.Equal(suite.T(), 1, savedLog.OperationType)
	assert.Equal(suite.T(), -1, savedLog.ChangeAmount)
	assert.Equal(suite.T(), "观看视频消耗", savedLog.Reason)
}

// TestGetUserBenefitLogs 测试获取用户权益日志
func (suite *BenefitModelTestSuite) TestGetUserBenefitLogs() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "benefituser005")
	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	benefit := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)

	// 创建多条权益日志
	logs := []*model.UserBenefitLog{
		{
			Uid:             testUser.Id,
			BenefitID:       benefit.Id,
			OperationType:   1, // 消耗
			ChangeAmount:    -1,
			BeforeValue:     5,
			AfterValue:      4,
			Reason:          "观看视频消耗",
			CreateTime:      time.Now(),
			CreateTimestamp: time.Now().UnixMilli(),
		},
		{
			Uid:             testUser.Id,
			BenefitID:       benefit.Id,
			OperationType:   2, // 分配
			ChangeAmount:    5,
			BeforeValue:     0,
			AfterValue:      5,
			Reason:          "每日权益分配",
			CreateTime:      time.Now().Add(-time.Hour),
			CreateTimestamp: time.Now().Add(-time.Hour).UnixMilli(),
		},
	}

	for _, log := range logs {
		err := suite.benefitModel.SaveOne(log)
		require.NoError(suite.T(), err)
	}

	// 获取用户权益日志
	var userLogs []model.UserBenefitLog
	err := suite.benefitModel.GetOrderedList(&userLogs, "create_timestamp DESC", model.UserBenefitLog{Uid: testUser.Id})
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), userLogs, 2)

	// 验证按时间倒序排列
	assert.True(suite.T(), userLogs[0].CreateTimestamp >= userLogs[1].CreateTimestamp)
}

// TestGetBenefitByCode 测试根据权益码获取权益
func (suite *BenefitModelTestSuite) TestGetBenefitByCode() {
	// 创建测试数据
	group := suite.dbHelper.CreateTestBenefitGroup(suite.T(), "VIDEO_LIMIT", "视频观看限制")
	benefit := suite.dbHelper.CreateTestBenefit(suite.T(), group.Id, "DAILY_VIDEO_LIMIT", "每日视频观看限制", 1, 5)

	// 根据权益码获取权益
	var foundBenefit model.Benefit
	found, err := suite.benefitModel.GetOne(&foundBenefit, model.Benefit{BenefitCode: benefit.BenefitCode})
	require.NoError(suite.T(), err)
	require.True(suite.T(), found)
	assert.Equal(suite.T(), benefit.Id, foundBenefit.Id)
	assert.Equal(suite.T(), benefit.BenefitCode, foundBenefit.BenefitCode)
	assert.Equal(suite.T(), benefit.BenefitName, foundBenefit.BenefitName)

	// 测试获取不存在的权益
	var notFoundBenefit model.Benefit
	found, err = suite.benefitModel.GetOne(&notFoundBenefit, model.Benefit{BenefitCode: constants.BenefitCode("NONEXISTENT_CODE")})
	require.NoError(suite.T(), err)
	assert.False(suite.T(), found)
}

// 运行测试套件
func TestBenefitModelSuite(t *testing.T) {
	suite.Run(t, new(BenefitModelTestSuite))
}
