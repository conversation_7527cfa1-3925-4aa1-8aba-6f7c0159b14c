package vip

import (
	"loop/internal/model"
	"loop/pkg/timex"
	"loop/test/utils"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// VipModelTestSuite VIP模型测试套件
type VipModelTestSuite struct {
	suite.Suite
	helper   *utils.TestHelper
	dbHelper *utils.DBHelper
	vipModel *model.VipModel
	ctx      *gin.Context
}

// SetupSuite 设置测试套件
func (suite *VipModelTestSuite) SetupSuite() {
	suite.helper = utils.NewTestHelper(suite.T())
	suite.dbHelper = utils.NewDBHelper(suite.helper.DB)

	// 创建测试上下文
	suite.ctx, _ = gin.CreateTestContext(nil)

	// 创建VIP模型实例
	dbModel := &model.DbModel{DBExtension: *suite.helper.DB}
	suite.vipModel = model.NewVipModel(dbModel, suite.helper.Config)
}

// TearDownSuite 清理测试套件
func (suite *VipModelTestSuite) TearDownSuite() {
	suite.helper.CleanupDB(suite.T())
}

// SetupTest 每个测试前的设置
func (suite *VipModelTestSuite) SetupTest() {
	// 清理测试数据
	tables := []string{
		"user_purchase_orders", "user_subscriptions", "user_vip_flows",
		"user_vip_relations", "promotion_codes", "trade_products",
	}
	suite.dbHelper.CleanupTestData(suite.T(), tables...)
}

// TestCreateVIP 测试创建VIP等级
func (suite *VipModelTestSuite) TestCreateVIP() {
	vip := &model.VIP{
		Name:  "测试VIP等级",
		Level: 500,
	}

	err := suite.vipModel.SaveOne(vip)
	require.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), vip.Id)
	assert.NotZero(suite.T(), vip.CreatedAt)
}

// TestGetUserVIPRelation 测试获取用户VIP关系
func (suite *VipModelTestSuite) TestGetUserVIPRelation() {
	// 创建测试用户和VIP关系
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "vipuser001")
	vip := suite.dbHelper.CreateTestVIP(suite.T(), "测试Pro", 100)
	relation := suite.dbHelper.CreateTestUserVIPRelation(suite.T(), testUser.Id, vip.Id, 30)

	// 测试获取存在的VIP关系
	var foundRelation model.UserVIPRelations
	found, err := suite.vipModel.GetOne(&foundRelation, model.UserVIPRelations{Uid: testUser.Id})
	require.NoError(suite.T(), err)
	require.True(suite.T(), found)
	assert.Equal(suite.T(), relation.Uid, foundRelation.Uid)
	assert.Equal(suite.T(), relation.VipID, foundRelation.VipID)
	assert.Equal(suite.T(), relation.IsVip, foundRelation.IsVip)

	// 测试获取不存在的VIP关系
	var notFoundRelation model.UserVIPRelations
	found, err = suite.vipModel.GetOne(&notFoundRelation, model.UserVIPRelations{Uid: "nonexistent"})
	require.NoError(suite.T(), err)
	assert.False(suite.T(), found)
}

// TestCreateUserVIPRelation 测试创建用户VIP关系
func (suite *VipModelTestSuite) TestCreateUserVIPRelation() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "newvipuser001")
	vip := suite.dbHelper.CreateTestVIP(suite.T(), "测试Ultra", 1000)

	expireTime := time.Now().AddDate(0, 0, 30)
	relation := &model.UserVIPRelations{
		Uid:             testUser.Id,
		IsVip:           1,
		VipID:           vip.Id,
		IsSubscription:  0,
		ExpireDate:      expireTime.Format("2006-01-02"),
		ExpireTimestamp: expireTime.UnixMilli(),
	}

	err := suite.vipModel.SaveOne(relation)
	require.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), relation.Id)

	// 验证关系已创建
	var savedRelation model.UserVIPRelations
	err = suite.helper.DB.First(&savedRelation, "uid = ?", testUser.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), relation.Uid, savedRelation.Uid)
	assert.Equal(suite.T(), relation.VipID, savedRelation.VipID)
}

// TestUpdateUserVIPRelation 测试更新用户VIP关系
func (suite *VipModelTestSuite) TestUpdateUserVIPRelation() {
	// 创建测试数据
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "updatevipuser001")
	vip := suite.dbHelper.CreateTestVIP(suite.T(), "测试Pro", 100)
	relation := suite.dbHelper.CreateTestUserVIPRelation(suite.T(), testUser.Id, vip.Id, 30)

	// 更新VIP关系
	newExpireTime := time.Now().AddDate(0, 0, 60)
	relation.IsVip = 1
	relation.ExpireDate = newExpireTime.Format("2006-01-02")
	relation.ExpireTimestamp = newExpireTime.UnixMilli()

	err := suite.vipModel.Update(relation, "id = ?", relation.Id)
	require.NoError(suite.T(), err)

	// 验证更新结果
	var updatedRelation model.UserVIPRelations
	err = suite.helper.DB.First(&updatedRelation, "uid = ?", testUser.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), newExpireTime.Format("2006-01-02"), updatedRelation.ExpireDate)
	assert.Equal(suite.T(), newExpireTime.UnixMilli(), updatedRelation.ExpireTimestamp)
}

// TestCreateTradeProduct 测试创建商品
func (suite *VipModelTestSuite) TestCreateTradeProduct() {
	vip := suite.dbHelper.CreateTestVIP(suite.T(), "测试Pro", 100)
	product := suite.dbHelper.CreateTestTradeProduct(suite.T(), "测试月度订阅", 9.99, 30, int(vip.Id))

	assert.NotEmpty(suite.T(), product.Id)
	assert.Equal(suite.T(), "测试月度订阅", product.Name)
	assert.Equal(suite.T(), 9.99, product.Price)
	assert.Equal(suite.T(), 30, product.Days)
	assert.Equal(suite.T(), vip.Id, product.VipID)
}

// TestGetTradeProductsByTerminal 测试根据终端获取商品
func (suite *VipModelTestSuite) TestGetTradeProductsByTerminal() {
	// 创建不同终端的商品
	vip := suite.dbHelper.CreateTestVIP(suite.T(), "测试Pro", 100)
	iosProduct := suite.dbHelper.CreateTestTradeProduct(suite.T(), "iOS月度订阅", 9.99, 30, int(vip.Id))
	iosProduct.Terminal = 2 // iOS
	suite.helper.DB.Save(iosProduct)

	androidProduct := suite.dbHelper.CreateTestTradeProduct(suite.T(), "Android月度订阅", 9.99, 30, int(vip.Id))
	androidProduct.Terminal = 1 // Android
	suite.helper.DB.Save(androidProduct)

	// 测试获取iOS商品
	var iosProducts []model.TradeProduct
	err := suite.vipModel.GetList(&iosProducts, model.TradeProduct{Terminal: 2})
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), iosProducts, 1)
	assert.Equal(suite.T(), "iOS月度订阅", iosProducts[0].Name)

	// 测试获取Android商品
	var androidProducts []model.TradeProduct
	err = suite.vipModel.GetList(&androidProducts, model.TradeProduct{Terminal: 1})
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), androidProducts, 1)
	assert.Equal(suite.T(), "Android月度订阅", androidProducts[0].Name)
}

// TestCreatePromotionCode 测试创建兑换码
func (suite *VipModelTestSuite) TestCreatePromotionCode() {
	days := 30
	vipLevel := 100

	createdCode, err := suite.vipModel.AddPromotionCode(days, vipLevel)
	require.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), createdCode)

	// 验证兑换码已创建
	var promotionCode model.PromotionCode
	err = suite.helper.DB.First(&promotionCode, "code = ?", createdCode).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), days, promotionCode.Days)
	assert.Equal(suite.T(), vipLevel, promotionCode.VipLevel)
	assert.Equal(suite.T(), 0, promotionCode.Status) // 未使用
}

// TestExchangeCode 测试兑换码兑换
func (suite *VipModelTestSuite) TestExchangeCode() {
	// 创建测试用户和兑换码
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "exchangeuser001")
	promotionCode := suite.dbHelper.CreateTestPromotionCode(suite.T(), "EXCHANGE_TEST_001", 30, 100)

	// 执行兑换
	err := suite.vipModel.ExchangeCode(suite.ctx, testUser.Id, promotionCode.Code)
	require.NoError(suite.T(), err)

	// 验证兑换码状态已更新
	var updatedCode model.PromotionCode
	err = suite.helper.DB.First(&updatedCode, "id = ?", promotionCode.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 1, updatedCode.Status) // 已使用
	assert.Equal(suite.T(), testUser.Id, updatedCode.Uid)
	assert.NotZero(suite.T(), updatedCode.RedeemedTimestamp)

	// 验证用户VIP关系已创建
	var vipRelation model.UserVIPRelations
	err = suite.helper.DB.First(&vipRelation, "uid = ?", testUser.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 1, vipRelation.IsVip)

	// 验证VIP流水已创建
	var vipFlow model.UserVIPFlow
	err = suite.helper.DB.First(&vipFlow, "uid = ?", testUser.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), testUser.Id, vipFlow.Uid)
	assert.Equal(suite.T(), 30, vipFlow.Days)
	assert.Equal(suite.T(), 2, vipFlow.Type) // 兑换码来源
}

// TestExchangeCodeAlreadyUsed 测试兑换已使用的兑换码
func (suite *VipModelTestSuite) TestExchangeCodeAlreadyUsed() {
	// 创建测试用户和已使用的兑换码
	testUser1 := suite.dbHelper.CreateTestUser(suite.T(), "exchangeuser002")
	testUser2 := suite.dbHelper.CreateTestUser(suite.T(), "exchangeuser003")

	promotionCode := suite.dbHelper.CreateTestPromotionCode(suite.T(), "USED_CODE_001", 30, 100)
	promotionCode.Status = 1 // 已使用
	promotionCode.Uid = testUser1.Id
	promotionCode.RedeemedTimestamp = timex.Now().UnixMilli()
	suite.helper.DB.Save(promotionCode)

	// 尝试再次兑换
	err := suite.vipModel.ExchangeCode(suite.ctx, testUser2.Id, promotionCode.Code)
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "已被使用") // 根据实际错误消息调整
}

// TestExchangeCodeNonexistent 测试兑换不存在的兑换码
func (suite *VipModelTestSuite) TestExchangeCodeNonexistent() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "exchangeuser004")

	err := suite.vipModel.ExchangeCode(suite.ctx, testUser.Id, "NONEXISTENT_CODE")
	assert.Error(suite.T(), err)
	assert.Contains(suite.T(), err.Error(), "不存在") // 根据实际错误消息调整
}

// TestCreateUserSubscription 测试创建用户订阅
func (suite *VipModelTestSuite) TestCreateUserSubscription() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "subuser001")
	product := suite.dbHelper.CreateTestTradeProduct(suite.T(), "订阅商品", 9.99, 30, 100)

	subscription := &model.UserSubscription{
		Uid:                        testUser.Id,
		ProductID:                  product.Id,
		ProductName:                product.Name,
		PaymentProvider:            1, // Apple
		OutTransactionId:           "test_transaction_001",
		AppleOriginalTransactionId: "test_original_001",
		FirstCycleAmount:           product.Price,
		NextCycleAmount:            product.Price,
		Currency:                   "USD",
		Status:                     1, // 活跃
		SignTimestamp:              timex.Now().UnixMilli(),
		NextPaidTimestamp:          timex.Now().AddDate(0, 0, 30).UnixMilli(),
		NextPaidDate:               timex.Now().AddDate(0, 0, 30).Format("2006-01-02"),
	}

	err := suite.vipModel.SaveOne(subscription)
	require.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), subscription.Id)

	// 验证订阅已创建
	var savedSubscription model.UserSubscription
	err = suite.helper.DB.First(&savedSubscription, "uid = ?", testUser.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), subscription.Uid, savedSubscription.Uid)
	assert.Equal(suite.T(), subscription.ProductID, savedSubscription.ProductID)
}

// TestCreatePurchaseOrder 测试创建购买订单
func (suite *VipModelTestSuite) TestCreatePurchaseOrder() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "orderuser001")
	product := suite.dbHelper.CreateTestTradeProduct(suite.T(), "订单商品", 19.99, 30, 100)

	order := &model.UserPurchaseOrder{
		Uid:                        testUser.Id,
		ProductID:                  product.Id,
		OrderNo:                    "TEST_ORDER_001",
		Amount:                     product.Price,
		Currency:                   "USD",
		PaymentProvider:            1, // Apple
		OutTransactionId:           "test_transaction_002",
		AppleOriginalTransactionId: "test_original_002",
		Status:                     1, // 待支付
		ProductName:                product.Name,
		ProductType:                1,
	}

	err := suite.vipModel.SaveOne(order)
	require.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), order.Id)

	// 验证订单已创建
	var savedOrder model.UserPurchaseOrder
	err = suite.helper.DB.First(&savedOrder, "order_no = ?", order.OrderNo).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), order.Uid, savedOrder.Uid)
	assert.Equal(suite.T(), order.Amount, savedOrder.Amount)
}

// TestGetUserVIPFlow 测试获取用户VIP流水
func (suite *VipModelTestSuite) TestGetUserVIPFlow() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "flowuser001")

	// 创建VIP流水记录
	flows := []*model.UserVIPFlow{
		{
			Uid:                testUser.Id,
			Title:              "苹果内购",
			Type:               1, // 订阅
			Operation:          1, // 开通
			OperationTimestamp: timex.Now().UnixMilli(),
			Days:               30,
			Terminal:           2, // iOS
			BizID:              "order_001",
		},
		{
			Uid:                testUser.Id,
			Title:              "兑换码赠送",
			Type:               2, // 兑换码
			Operation:          1, // 开通
			OperationTimestamp: timex.Now().AddDate(0, 0, -7).UnixMilli(),
			Days:               7,
			Terminal:           0, // 未知
			BizID:              "code_001",
		},
	}

	for _, flow := range flows {
		err := suite.helper.DB.Create(flow).Error
		require.NoError(suite.T(), err)
	}

	// 获取用户VIP流水
	var userFlows []model.UserVIPFlow
	err := suite.vipModel.GetOrderedList(&userFlows, "operation_timestamp DESC", model.UserVIPFlow{Uid: testUser.Id})
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), userFlows, 2)

	// 验证按时间倒序排列
	assert.True(suite.T(), userFlows[0].OperationTimestamp >= userFlows[1].OperationTimestamp)
}

// 运行测试套件
func TestVipModelSuite(t *testing.T) {
	suite.Run(t, new(VipModelTestSuite))
}
