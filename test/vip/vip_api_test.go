package vip

import (
	"bytes"
	"encoding/json"
	"loop/internal/api"
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/request"
	"loop/test/utils"
	"net/http/httptest"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// VipApiTestSuite VIP API测试套件
type VipApiTestSuite struct {
	suite.Suite
	helper       *utils.TestHelper
	dbHelper     *utils.DBHelper
	assertHelper *utils.AssertHelper
	vipApi       *api.VipApi
	router       *gin.Engine
}

// SetupSuite 设置测试套件
func (suite *VipApiTestSuite) SetupSuite() {
	suite.helper = utils.NewTestHelper(suite.T())
	suite.dbHelper = utils.NewDBHelper(suite.helper.DB)
	suite.assertHelper = utils.NewAssertHelper(suite.T())

	// 创建API实例
	dbModel := &model.DbModel{DBExtension: *suite.helper.DB}
	vipModel := model.NewVipModel(dbModel, suite.helper.Config)
	vipRepo := data.NewVipRepo(vipModel)
	suite.vipApi = api.NewVipApi(vipRepo, dbModel)

	// 设置路由
	suite.router = gin.New()
	suite.setupRoutes()
}

// setupRoutes 设置测试路由
func (suite *VipApiTestSuite) setupRoutes() {
	v1 := suite.router.Group("/api/v1")

	// 需要认证的路由
	protected := v1.Group("/")
	protected.Use(suite.mockJWTMiddleware())
	{
		protected.GET("/vip/products", suite.vipApi.GetProductList)
		protected.POST("/vip/exchange", suite.vipApi.ExchangeCode)
		protected.GET("/vip/info", suite.vipApi.GetUserVipInfo)
		protected.GET("/vip/flows", suite.vipApi.GetUserVipFlowList)
		protected.GET("/vip/orders", suite.vipApi.GetOrderList)
		protected.POST("/vip/order", suite.vipApi.CreatePayOrder)
	}

	// 管理员路由
	admin := v1.Group("/admin")
	admin.Use(suite.mockAdminMiddleware())
	{
		admin.POST("/vip/promotion-code", suite.vipApi.AddPromotionCode)
	}
}

// mockJWTMiddleware 模拟JWT中间件
func (suite *VipApiTestSuite) mockJWTMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.GetHeader("X-Test-UID")
		platform := c.GetHeader("X-Test-Platform")
		if uid != "" {
			c.Set("uid", uid)
			c.Set("username", "testuser")
			c.Set("status", 0)
		}
		if platform != "" {
			c.Set("platform", platform)
		}
		c.Next()
	}
}

// mockAdminMiddleware 模拟管理员中间件
func (suite *VipApiTestSuite) mockAdminMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		c.Set("admin", true)
		c.Next()
	}
}

// TearDownSuite 清理测试套件
func (suite *VipApiTestSuite) TearDownSuite() {
	suite.helper.CleanupDB(suite.T())
}

// SetupTest 每个测试前的设置
func (suite *VipApiTestSuite) SetupTest() {
	// 清理测试数据
	tables := []string{
		"user_purchase_orders", "user_subscriptions", "user_vip_flows",
		"user_vip_relations", "promotion_codes", "trade_products",
	}
	suite.dbHelper.CleanupTestData(suite.T(), tables...)
}

// TestGetProductListAPI 测试获取商品列表API
func (suite *VipApiTestSuite) TestGetProductListAPI() {
	// 创建测试商品
	vip := suite.dbHelper.CreateTestVIP(suite.T(), "测试Pro", 100)
	product := suite.dbHelper.CreateTestTradeProduct(suite.T(), "iOS商品", 9.99, 30, int(vip.Id))
	product.Terminal = 2 // iOS
	suite.helper.DB.Save(product)

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/vip/products", nil)
	req.Header.Set("X-Test-UID", "test_user_001")
	req.Header.Set("X-Test-Platform", "iOS")

	suite.router.ServeHTTP(w, req)

	suite.assertHelper.AssertSuccessResponse(w, nil)
	suite.assertHelper.AssertListResponse(w, 1)
}

// TestExchangeCodeAPI 测试兑换码兑换API
func (suite *VipApiTestSuite) TestExchangeCodeAPI() {
	// 创建测试用户和兑换码
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "exchangeapiuser001")
	promotionCode := suite.dbHelper.CreateTestPromotionCode(suite.T(), "API_EXCHANGE_001", 30, 100)

	reqData := request.ExchangeCodeReq{
		Code: promotionCode.Code,
	}

	jsonData, _ := json.Marshal(reqData)
	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("POST", "/api/v1/vip/exchange", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证兑换码状态已更新
	var updatedCode model.PromotionCode
	err := suite.helper.DB.First(&updatedCode, "id = ?", promotionCode.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 1, updatedCode.Status) // 已使用
}

// TestExchangeCodeAPIInvalid 测试兑换无效兑换码API
func (suite *VipApiTestSuite) TestExchangeCodeAPIInvalid() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "exchangeapiuser002")

	reqData := request.ExchangeCodeReq{
		Code: "INVALID_CODE",
	}

	jsonData, _ := json.Marshal(reqData)
	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("POST", "/api/v1/vip/exchange", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertErrorResponse(w, 400, "")
}

// TestAddPromotionCodeAPI 测试添加兑换码API
func (suite *VipApiTestSuite) TestAddPromotionCodeAPI() {
	reqData := request.AddPromotionCodeReq{
		Days:     30,
		VipLevel: 100,
	}

	jsonData, _ := json.Marshal(reqData)
	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("POST", "/api/v1/admin/vip/promotion-code", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证兑换码已创建
	var count int64
	suite.helper.DB.Model(&model.PromotionCode{}).Where("days = ? AND vip_level = ?", 30, 100).Count(&count)
	assert.Equal(suite.T(), int64(1), count)
}

// TestGetUserVipInfoAPI 测试获取用户VIP信息API
func (suite *VipApiTestSuite) TestGetUserVipInfoAPI() {
	// 创建测试用户和VIP关系
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "vipapiuser001")
	vip := suite.dbHelper.CreateTestVIP(suite.T(), "测试Pro", 100)
	relation := suite.dbHelper.CreateTestUserVIPRelation(suite.T(), testUser.Id, vip.Id, 30)

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/vip/info", nil)
	req.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, req)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证返回的VIP信息
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)

	data, ok := response["data"].(map[string]interface{})
	require.True(suite.T(), ok)
	assert.Equal(suite.T(), float64(relation.IsVip), data["is_vip"])
}

// TestGetUserVipFlowListAPI 测试获取用户VIP流水列表API
func (suite *VipApiTestSuite) TestGetUserVipFlowListAPI() {
	// 创建测试用户和VIP流水
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "flowapiuser001")

	flow := &model.UserVIPFlow{
		Uid:                testUser.Id,
		Title:              "测试流水",
		Type:               1, // 订阅
		Operation:          1, // 开通
		OperationTimestamp: 1703001600000,
		Days:               30,
		Terminal:           2, // iOS
		BizID:              "test_order_001",
	}
	err := suite.helper.DB.Create(flow).Error
	require.NoError(suite.T(), err)

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/vip/flows", nil)
	req.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, req)

	suite.assertHelper.AssertSuccessResponse(w, nil)
	suite.assertHelper.AssertListResponse(w, 1)
}

// TestGetOrderListAPI 测试获取订单列表API
func (suite *VipApiTestSuite) TestGetOrderListAPI() {
	// 创建测试用户和订单
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "orderapiuser001")
	vip := suite.dbHelper.CreateTestVIP(suite.T(), "测试Pro", 100)
	product := suite.dbHelper.CreateTestTradeProduct(suite.T(), "测试商品", 9.99, 30, int(vip.Id))

	order := &model.UserPurchaseOrder{
		Uid:                        testUser.Id,
		ProductID:                  product.Id,
		OrderNo:                    "API_TEST_ORDER_001",
		Amount:                     product.Price,
		Currency:                   "USD",
		PaymentProvider:            1, // Apple
		OutTransactionId:           "test_transaction_001",
		AppleOriginalTransactionId: "test_original_001",
		Status:                     2, // 交易完成
		ProductName:                product.Name,
		ProductType:                1,
		PaidTimestamp:              1703001600000,
	}
	err := suite.helper.DB.Create(order).Error
	require.NoError(suite.T(), err)

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/vip/orders", nil)
	req.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, req)

	suite.assertHelper.AssertSuccessResponse(w, nil)
	suite.assertHelper.AssertListResponse(w, 1)
}

// TestCreatePayOrderAPI 测试创建支付订单API
func (suite *VipApiTestSuite) TestCreatePayOrderAPI() {
	// 创建测试用户和商品
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "payapiuser001")
	vip := suite.dbHelper.CreateTestVIP(suite.T(), "测试Pro", 100)
	product := suite.dbHelper.CreateTestTradeProduct(suite.T(), "测试商品", 9.99, 30, int(vip.Id))

	reqData := request.CreatePayOrderReq{
		ProductId: product.Id,
	}

	jsonData, _ := json.Marshal(reqData)
	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("POST", "/api/v1/vip/order", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("X-Test-UID", testUser.Id)
	httpReq.Header.Set("X-Test-Platform", "iOS")

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证订单已创建
	var count int64
	suite.helper.DB.Model(&model.UserPurchaseOrder{}).Where("uid = ? AND product_id = ?", testUser.Id, product.Id).Count(&count)
	assert.Equal(suite.T(), int64(1), count)
}

// TestUnauthorizedAccess 测试未授权访问
func (suite *VipApiTestSuite) TestUnauthorizedAccess() {
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/vip/info", nil)
	// 不设置X-Test-UID头部

	suite.router.ServeHTTP(w, req)

	// 根据实际的中间件实现调整期望的状态码
	assert.NotEqual(suite.T(), 200, w.Code)
}

// 运行测试套件
func TestVipApiSuite(t *testing.T) {
	suite.Run(t, new(VipApiTestSuite))
}
