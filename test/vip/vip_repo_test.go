package vip

import (
	"loop/internal/data"
	"loop/internal/model"
	"loop/internal/request"
	"loop/pkg/jwtx"
	"loop/pkg/web"
	"loop/test/utils"
	"testing"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// VipRepoTestSuite VIP数据层测试套件
type VipRepoTestSuite struct {
	suite.Suite
	helper      *utils.TestHelper
	dbHelper    *utils.DBHelper
	vipRepo     *data.VipRepo
	ctx         *gin.Context
}

// SetupSuite 设置测试套件
func (suite *VipRepoTestSuite) SetupSuite() {
	suite.helper = utils.NewTestHelper(suite.T())
	suite.dbHelper = utils.NewDBHelper(suite.helper.DB)

	// 创建测试上下文
	suite.ctx, _ = gin.CreateTestContext(nil)

	// 创建VIP仓库实例
	dbModel := &model.DbModel{DBExtension: *suite.helper.DB}
	vipModel := model.NewVipModel(dbModel, suite.helper.Config)
	suite.vipRepo = data.NewVipRepo(vipModel)
}

// TearDownSuite 清理测试套件
func (suite *VipRepoTestSuite) TearDownSuite() {
	suite.helper.CleanupDB(suite.T())
}

// SetupTest 每个测试前的设置
func (suite *VipRepoTestSuite) SetupTest() {
	// 清理测试数据
	tables := []string{
		"user_purchase_orders", "user_subscriptions", "user_vip_flows",
		"user_vip_relations", "promotion_codes", "trade_products",
	}
	suite.dbHelper.CleanupTestData(suite.T(), tables...)
}

// TestGetProductList 测试获取商品列表
func (suite *VipRepoTestSuite) TestGetProductList() {
	// 创建测试商品
	vip := suite.dbHelper.CreateTestVIP(suite.T(), "测试Pro", 100)
	iosProduct := suite.dbHelper.CreateTestTradeProduct(suite.T(), "iOS商品", 9.99, 30, int(vip.Id))
	iosProduct.Terminal = 2 // iOS
	suite.helper.DB.Save(iosProduct)

	androidProduct := suite.dbHelper.CreateTestTradeProduct(suite.T(), "Android商品", 9.99, 30, int(vip.Id))
	androidProduct.Terminal = 1 // Android
	suite.helper.DB.Save(androidProduct)

	// 测试iOS平台
	suite.ctx.Set("platform", "iOS")
	result := suite.vipRepo.GetProductList(suite.ctx)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证返回的商品数据
	products, ok := result.Data.([]interface{})
	require.True(suite.T(), ok)
	assert.Len(suite.T(), products, 1)

	// 测试Android平台
	suite.ctx.Set("platform", "Android")
	result = suite.vipRepo.GetProductList(suite.ctx)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	products, ok = result.Data.([]interface{})
	require.True(suite.T(), ok)
	assert.Len(suite.T(), products, 1)
}

// TestExchangeCode 测试兑换码兑换
func (suite *VipRepoTestSuite) TestExchangeCode() {
	// 创建测试用户和兑换码
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "exchangeuser001")
	promotionCode := suite.dbHelper.CreateTestPromotionCode(suite.T(), "EXCHANGE_TEST_001", 30, 100)

	// 设置JWT上下文
	suite.ctx.Set("uid", testUser.Id)

	req := request.ExchangeCodeReq{
		Code: promotionCode.Code,
	}

	result := suite.vipRepo.ExchangeCode(suite.ctx, req)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证兑换码状态已更新
	var updatedCode model.PromotionCode
	err := suite.helper.DB.First(&updatedCode, "id = ?", promotionCode.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 1, updatedCode.Status) // 已使用
	assert.Equal(suite.T(), testUser.Id, updatedCode.Uid)

	// 验证用户VIP关系已创建
	var vipRelation model.UserVIPRelations
	err = suite.helper.DB.First(&vipRelation, "uid = ?", testUser.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), 1, vipRelation.IsVip)
}

// TestExchangeCodeInvalid 测试兑换无效兑换码
func (suite *VipRepoTestSuite) TestExchangeCodeInvalid() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "exchangeuser002")
	suite.ctx.Set("uid", testUser.Id)

	req := request.ExchangeCodeReq{
		Code: "INVALID_CODE",
	}

	result := suite.vipRepo.ExchangeCode(suite.ctx, req)
	require.NotNil(suite.T(), result)
	assert.NotEqual(suite.T(), web.SUCCESS, result.Code)
}

// TestAddPromotionCode 测试添加兑换码
func (suite *VipRepoTestSuite) TestAddPromotionCode() {
	req := request.AddPromotionCodeReq{
		Days:     30,
		VipLevel: 100,
	}

	result := suite.vipRepo.AddPromotionCode(suite.ctx, req)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证返回了兑换码
	code, ok := result.Data.(string)
	require.True(suite.T(), ok)
	assert.NotEmpty(suite.T(), code)

	// 验证兑换码已创建
	var promotionCode model.PromotionCode
	err := suite.helper.DB.First(&promotionCode, "code = ?", code).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), req.Days, promotionCode.Days)
	assert.Equal(suite.T(), req.VipLevel, promotionCode.VipLevel)
	assert.Equal(suite.T(), 0, promotionCode.Status) // 未使用
}

// TestGetUserVipInfo 测试获取用户VIP信息
func (suite *VipRepoTestSuite) TestGetUserVipInfo() {
	// 创建测试用户和VIP关系
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "vipinfouser001")
	vip := suite.dbHelper.CreateTestVIP(suite.T(), "测试Pro", 100)
	relation := suite.dbHelper.CreateTestUserVIPRelation(suite.T(), testUser.Id, vip.Id, 30)

	// 设置JWT上下文
	suite.ctx.Set("uid", testUser.Id)

	result := suite.vipRepo.GetUserVipInfo(suite.ctx)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证返回的VIP信息
	data, ok := result.Data.(map[string]interface{})
	require.True(suite.T(), ok)
	assert.Equal(suite.T(), relation.IsVip, int(data["is_vip"].(float64)))
	assert.Equal(suite.T(), relation.VipID, uint(data["vip_id"].(float64)))
}

// TestGetUserVipInfoNoVip 测试获取无VIP用户信息
func (suite *VipRepoTestSuite) TestGetUserVipInfoNoVip() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "novipuser001")
	suite.ctx.Set("uid", testUser.Id)

	result := suite.vipRepo.GetUserVipInfo(suite.ctx)
	require.NotNil(suite.T(), result)
	// 根据实际实现，这里可能返回空数据或默认值
	// 需要根据实际的GetUserVipInfo实现来调整断言
}

// TestGetUserVipFlowList 测试获取用户VIP流水列表
func (suite *VipRepoTestSuite) TestGetUserVipFlowList() {
	// 创建测试用户和VIP流水
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "flowuser001")

	flows := []*model.UserVIPFlow{
		{
			Uid:                testUser.Id,
			Title:              "苹果内购",
			Type:               1, // 订阅
			Operation:          1, // 开通
			OperationTimestamp: 1703001600000,
			Days:               30,
			Terminal:           2, // iOS
			BizID:              "order_001",
		},
		{
			Uid:                testUser.Id,
			Title:              "兑换码赠送",
			Type:               2, // 兑换码
			Operation:          1, // 开通
			OperationTimestamp: 1703002600000,
			Days:               7,
			Terminal:           0, // 未知
			BizID:              "code_001",
		},
	}

	for _, flow := range flows {
		err := suite.helper.DB.Create(flow).Error
		require.NoError(suite.T(), err)
	}

	// 设置JWT上下文
	suite.ctx.Set("uid", testUser.Id)

	result := suite.vipRepo.GetUserVipFlowList(suite.ctx)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证返回的流水数据
	data, ok := result.Data.([]interface{})
	require.True(suite.T(), ok)
	assert.Len(suite.T(), data, 2)
}

// TestGetOrderList 测试获取订单列表
func (suite *VipRepoTestSuite) TestGetOrderList() {
	// 创建测试用户和订单
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "orderuser001")
	vip := suite.dbHelper.CreateTestVIP(suite.T(), "测试Pro", 100)
	product := suite.dbHelper.CreateTestTradeProduct(suite.T(), "测试商品", 9.99, 30, int(vip.Id))

	order := &model.UserPurchaseOrder{
		Uid:                        testUser.Id,
		ProductID:                  product.Id,
		OrderNo:                    "TEST_ORDER_001",
		Amount:                     product.Price,
		Currency:                   "USD",
		PaymentProvider:            1, // Apple
		OutTransactionId:           "test_transaction_001",
		AppleOriginalTransactionId: "test_original_001",
		Status:                     2, // 交易完成
		ProductName:                product.Name,
		ProductType:                1,
		PaidTimestamp:              1703001600000,
	}
	err := suite.helper.DB.Create(order).Error
	require.NoError(suite.T(), err)

	// 设置JWT上下文
	suite.ctx.Set("uid", testUser.Id)

	result := suite.vipRepo.GetOrderList(suite.ctx)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证返回的订单数据
	data, ok := result.Data.([]interface{})
	require.True(suite.T(), ok)
	assert.Len(suite.T(), data, 1)
}

// TestCreatePayOrder 测试创建支付订单
func (suite *VipRepoTestSuite) TestCreatePayOrder() {
	// 创建测试用户和商品
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "payuser001")
	vip := suite.dbHelper.CreateTestVIP(suite.T(), "测试Pro", 100)
	product := suite.dbHelper.CreateTestTradeProduct(suite.T(), "测试商品", 9.99, 30, int(vip.Id))

	// 设置JWT上下文
	suite.ctx.Set("uid", testUser.Id)
	suite.ctx.Set("platform", "iOS")

	req := request.CreatePayOrderReq{
		ProductId: product.Id,
	}

	result := suite.vipRepo.CreatePayOrder(suite.ctx, req)
	require.NotNil(suite.T(), result)
	assert.Equal(suite.T(), web.SUCCESS, result.Code)

	// 验证返回了订单号
	orderNo, ok := result.Data.(string)
	require.True(suite.T(), ok)
	assert.NotEmpty(suite.T(), orderNo)

	// 验证订单已创建
	var order model.UserPurchaseOrder
	err := suite.helper.DB.First(&order, "order_no = ?", orderNo).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), testUser.Id, order.Uid)
	assert.Equal(suite.T(), product.Id, order.ProductID)
	assert.Equal(suite.T(), 1, order.Status) // 待支付
}

// 运行测试套件
func TestVipRepoSuite(t *testing.T) {
	suite.Run(t, new(VipRepoTestSuite))
}
