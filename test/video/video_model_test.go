package video

import (
	"loop/internal/model"
	"loop/test/utils"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// VideoModelTestSuite 视频模型测试套件
type VideoModelTestSuite struct {
	suite.Suite
	helper     *utils.TestHelper
	dbHelper   *utils.DBHelper
	videoModel *model.VideoModel
	ctx        *gin.Context
}

// SetupSuite 设置测试套件
func (suite *VideoModelTestSuite) SetupSuite() {
	suite.helper = utils.NewTestHelper(suite.T())
	suite.dbHelper = utils.NewDBHelper(suite.helper.DB)

	// 创建测试上下文
	suite.ctx, _ = gin.CreateTestContext(nil)

	// 创建视频模型实例
	dbModel := &model.DbModel{DBExtension: *suite.helper.DB}
	suite.videoModel = model.NewVideoModel(dbModel, suite.helper.Config)
}

// TearDownSuite 清理测试套件
func (suite *VideoModelTestSuite) TearDownSuite() {
	suite.helper.CleanupDB(suite.T())
}

// SetupTest 每个测试前的设置
func (suite *VideoModelTestSuite) SetupTest() {
	// 清理测试数据
	tables := []string{
		"user_remote_resource_relations", "user_local_resources",
		"featured_contents", "series_resource_relations",
		"category_resource_relations", "resource_relations",
		"resources", "series_relations", "series",
		"categories", "category_types",
	}
	suite.dbHelper.CleanupTestData(suite.T(), tables...)
}

// TestCreateResource 测试创建资源
func (suite *VideoModelTestSuite) TestCreateResource() {
	resource := suite.dbHelper.CreateTestResource(suite.T(), "测试视频", "beginner", 300)

	assert.NotEmpty(suite.T(), resource.Id)
	assert.Equal(suite.T(), "测试视频", resource.Title)
	assert.Equal(suite.T(), "beginner", resource.Level)
	assert.Equal(suite.T(), 300, resource.Duration)
	assert.NotZero(suite.T(), resource.CreatedAt)
}

// TestGetResourcesByLevel 测试根据难度获取资源
func (suite *VideoModelTestSuite) TestGetResourcesByLevel() {
	// 创建不同难度的资源
	beginnerResource := suite.dbHelper.CreateTestResource(suite.T(), "初级视频", "beginner", 300)
	intermediateResource := suite.dbHelper.CreateTestResource(suite.T(), "中级视频", "intermediate", 400)
	advancedResource := suite.dbHelper.CreateTestResource(suite.T(), "高级视频", "advanced", 500)

	// 获取初级资源
	var beginnerResources []model.Resource
	err := suite.videoModel.GetList(&beginnerResources, model.Resource{Level: "beginner"})
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), beginnerResources, 1)
	assert.Equal(suite.T(), beginnerResource.Id, beginnerResources[0].Id)

	// 获取中级资源
	var intermediateResources []model.Resource
	err = suite.videoModel.GetList(&intermediateResources, model.Resource{Level: "intermediate"})
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), intermediateResources, 1)
	assert.Equal(suite.T(), intermediateResource.Id, intermediateResources[0].Id)

	// 获取高级资源
	var advancedResources []model.Resource
	err = suite.videoModel.GetList(&advancedResources, model.Resource{Level: "advanced"})
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), advancedResources, 1)
	assert.Equal(suite.T(), advancedResource.Id, advancedResources[0].Id)
}

// TestCreateSeries 测试创建系列
func (suite *VideoModelTestSuite) TestCreateSeries() {
	series := suite.dbHelper.CreateTestSeries(suite.T(), "测试系列")

	assert.NotEmpty(suite.T(), series.Id)
	assert.Equal(suite.T(), "测试系列", series.Name)
	assert.NotZero(suite.T(), series.CreatedAt)
}

// TestCreateSeriesResourceRelation 测试创建系列资源关系
func (suite *VideoModelTestSuite) TestCreateSeriesResourceRelation() {
	// 创建系列和资源
	series := suite.dbHelper.CreateTestSeries(suite.T(), "测试系列")
	resource := suite.dbHelper.CreateTestResource(suite.T(), "测试视频", "beginner", 300)

	// 创建系列资源关系
	relation := &model.SeriesResourceRelation{
		SeriesId:   series.Id,
		ResourceId: resource.Id,
		SortOrder:  1,
	}

	err := suite.videoModel.SaveOne(relation)
	require.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), relation.Id)

	// 验证关系已创建
	var savedRelation model.SeriesResourceRelation
	err = suite.helper.DB.First(&savedRelation, "series_id = ? AND resource_id = ?", series.Id, resource.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), series.Id, savedRelation.SeriesId)
	assert.Equal(suite.T(), resource.Id, savedRelation.ResourceId)
	assert.Equal(suite.T(), 1, savedRelation.SortOrder)
}

// TestGetSeriesResources 测试获取系列资源
func (suite *VideoModelTestSuite) TestGetSeriesResources() {
	// 创建系列和多个资源
	series := suite.dbHelper.CreateTestSeries(suite.T(), "测试系列")
	resource1 := suite.dbHelper.CreateTestResource(suite.T(), "视频1", "beginner", 300)
	resource2 := suite.dbHelper.CreateTestResource(suite.T(), "视频2", "beginner", 400)
	resource3 := suite.dbHelper.CreateTestResource(suite.T(), "视频3", "beginner", 500)

	// 创建系列资源关系
	relations := []*model.SeriesResourceRelation{
		{SeriesId: series.Id, ResourceId: resource1.Id, SortOrder: 1},
		{SeriesId: series.Id, ResourceId: resource2.Id, SortOrder: 2},
		{SeriesId: series.Id, ResourceId: resource3.Id, SortOrder: 3},
	}

	for _, relation := range relations {
		err := suite.videoModel.SaveOne(relation)
		require.NoError(suite.T(), err)
	}

	// 获取系列资源关系
	var seriesRelations []model.SeriesResourceRelation
	err := suite.videoModel.GetOrderedList(&seriesRelations, "sort_order ASC", 
		model.SeriesResourceRelation{SeriesId: series.Id})
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), seriesRelations, 3)

	// 验证按排序顺序
	assert.Equal(suite.T(), resource1.Id, seriesRelations[0].ResourceId)
	assert.Equal(suite.T(), resource2.Id, seriesRelations[1].ResourceId)
	assert.Equal(suite.T(), resource3.Id, seriesRelations[2].ResourceId)
}

// TestCreateCategory 测试创建分类
func (suite *VideoModelTestSuite) TestCreateCategory() {
	// 先创建分类类型
	categoryType := suite.dbHelper.CreateTestCategoryType(suite.T(), "难度等级")
	
	// 创建分类
	category := suite.dbHelper.CreateTestCategory(suite.T(), "初级", categoryType.Id)

	assert.NotEmpty(suite.T(), category.Id)
	assert.Equal(suite.T(), "初级", category.Name)
	assert.Equal(suite.T(), categoryType.Id, category.CategoryTypeId)
	assert.NotZero(suite.T(), category.CreatedAt)
}

// TestCreateCategoryResourceRelation 测试创建分类资源关系
func (suite *VideoModelTestSuite) TestCreateCategoryResourceRelation() {
	// 创建分类类型、分类和资源
	categoryType := suite.dbHelper.CreateTestCategoryType(suite.T(), "难度等级")
	category := suite.dbHelper.CreateTestCategory(suite.T(), "初级", categoryType.Id)
	resource := suite.dbHelper.CreateTestResource(suite.T(), "测试视频", "beginner", 300)

	// 创建分类资源关系
	relation := &model.CategoryResourceRelation{
		CategoryId: category.Id,
		ResourceId: resource.Id,
	}

	err := suite.videoModel.SaveOne(relation)
	require.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), relation.Id)

	// 验证关系已创建
	var savedRelation model.CategoryResourceRelation
	err = suite.helper.DB.First(&savedRelation, "category_id = ? AND resource_id = ?", category.Id, resource.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), category.Id, savedRelation.CategoryId)
	assert.Equal(suite.T(), resource.Id, savedRelation.ResourceId)
}

// TestCreateUserLocalResource 测试创建用户本地资源
func (suite *VideoModelTestSuite) TestCreateUserLocalResource() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "videouser001")
	resource := suite.dbHelper.CreateTestResource(suite.T(), "测试视频", "beginner", 300)

	// 创建用户本地资源
	localResource := &model.UserLocalResource{
		Uid:        testUser.Id,
		ResourceId: resource.Id,
		LocalPath:  "/local/path/video.mp4",
		FileSize:   1024000, // 1MB
		Status:     1,       // 下载完成
		Timestamp:  time.Now().UnixMilli(),
	}

	err := suite.videoModel.SaveOne(localResource)
	require.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), localResource.Id)

	// 验证本地资源已创建
	var savedLocalResource model.UserLocalResource
	err = suite.helper.DB.First(&savedLocalResource, "uid = ? AND resource_id = ?", testUser.Id, resource.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), testUser.Id, savedLocalResource.Uid)
	assert.Equal(suite.T(), resource.Id, savedLocalResource.ResourceId)
	assert.Equal(suite.T(), "/local/path/video.mp4", savedLocalResource.LocalPath)
	assert.Equal(suite.T(), int64(1024000), savedLocalResource.FileSize)
	assert.Equal(suite.T(), 1, savedLocalResource.Status)
}

// TestGetUserLocalResources 测试获取用户本地资源
func (suite *VideoModelTestSuite) TestGetUserLocalResources() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "videouser002")
	resource1 := suite.dbHelper.CreateTestResource(suite.T(), "视频1", "beginner", 300)
	resource2 := suite.dbHelper.CreateTestResource(suite.T(), "视频2", "beginner", 400)

	// 创建用户本地资源
	localResources := []*model.UserLocalResource{
		{
			Uid:        testUser.Id,
			ResourceId: resource1.Id,
			LocalPath:  "/local/path/video1.mp4",
			FileSize:   1024000,
			Status:     1, // 下载完成
			Timestamp:  time.Now().UnixMilli(),
		},
		{
			Uid:        testUser.Id,
			ResourceId: resource2.Id,
			LocalPath:  "/local/path/video2.mp4",
			FileSize:   2048000,
			Status:     0, // 下载中
			Timestamp:  time.Now().UnixMilli() + 1000,
		},
	}

	for _, localResource := range localResources {
		err := suite.videoModel.SaveOne(localResource)
		require.NoError(suite.T(), err)
	}

	// 获取用户本地资源
	var userLocalResources []model.UserLocalResource
	err := suite.videoModel.GetOrderedList(&userLocalResources, "timestamp DESC", 
		model.UserLocalResource{Uid: testUser.Id})
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), userLocalResources, 2)

	// 验证按时间倒序排列
	assert.True(suite.T(), userLocalResources[0].Timestamp >= userLocalResources[1].Timestamp)
}

// TestCreateUserRemoteResourceRelation 测试创建用户远程资源关系
func (suite *VideoModelTestSuite) TestCreateUserRemoteResourceRelation() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "videouser003")
	resource := suite.dbHelper.CreateTestResource(suite.T(), "测试视频", "beginner", 300)

	// 创建用户远程资源关系
	relation := &model.UserRemoteResourceRelation{
		Uid:        testUser.Id,
		ResourceId: resource.Id,
		RemoteUrl:  "https://remote.com/video.mp4",
		Status:     1, // 可用
		Timestamp:  time.Now().UnixMilli(),
	}

	err := suite.videoModel.SaveOne(relation)
	require.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), relation.Id)

	// 验证远程资源关系已创建
	var savedRelation model.UserRemoteResourceRelation
	err = suite.helper.DB.First(&savedRelation, "uid = ? AND resource_id = ?", testUser.Id, resource.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), testUser.Id, savedRelation.Uid)
	assert.Equal(suite.T(), resource.Id, savedRelation.ResourceId)
	assert.Equal(suite.T(), "https://remote.com/video.mp4", savedRelation.RemoteUrl)
	assert.Equal(suite.T(), 1, savedRelation.Status)
}

// TestCreateFeaturedContent 测试创建特色内容
func (suite *VideoModelTestSuite) TestCreateFeaturedContent() {
	resource := suite.dbHelper.CreateTestResource(suite.T(), "特色视频", "intermediate", 600)

	// 创建特色内容
	featured := &model.FeaturedContent{
		ResourceId:  resource.Id,
		Title:       "本周推荐",
		Description: "精选优质内容",
		CoverUrl:    "https://test.com/cover.jpg",
		SortOrder:   1,
		Status:      1, // 启用
	}

	err := suite.videoModel.SaveOne(featured)
	require.NoError(suite.T(), err)
	assert.NotEmpty(suite.T(), featured.Id)

	// 验证特色内容已创建
	var savedFeatured model.FeaturedContent
	err = suite.helper.DB.First(&savedFeatured, "resource_id = ?", resource.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), resource.Id, savedFeatured.ResourceId)
	assert.Equal(suite.T(), "本周推荐", savedFeatured.Title)
	assert.Equal(suite.T(), "精选优质内容", savedFeatured.Description)
	assert.Equal(suite.T(), 1, savedFeatured.SortOrder)
	assert.Equal(suite.T(), 1, savedFeatured.Status)
}

// TestGetFeaturedContents 测试获取特色内容
func (suite *VideoModelTestSuite) TestGetFeaturedContents() {
	// 创建多个特色内容
	resource1 := suite.dbHelper.CreateTestResource(suite.T(), "特色视频1", "beginner", 300)
	resource2 := suite.dbHelper.CreateTestResource(suite.T(), "特色视频2", "intermediate", 400)

	featuredContents := []*model.FeaturedContent{
		{
			ResourceId:  resource1.Id,
			Title:       "推荐1",
			Description: "描述1",
			SortOrder:   1,
			Status:      1,
		},
		{
			ResourceId:  resource2.Id,
			Title:       "推荐2",
			Description: "描述2",
			SortOrder:   2,
			Status:      1,
		},
	}

	for _, featured := range featuredContents {
		err := suite.videoModel.SaveOne(featured)
		require.NoError(suite.T(), err)
	}

	// 获取启用的特色内容
	var activeFeatured []model.FeaturedContent
	err := suite.videoModel.GetOrderedList(&activeFeatured, "sort_order ASC", 
		model.FeaturedContent{Status: 1})
	require.NoError(suite.T(), err)
	assert.Len(suite.T(), activeFeatured, 2)

	// 验证按排序顺序
	assert.Equal(suite.T(), resource1.Id, activeFeatured[0].ResourceId)
	assert.Equal(suite.T(), resource2.Id, activeFeatured[1].ResourceId)
	assert.Equal(suite.T(), "推荐1", activeFeatured[0].Title)
	assert.Equal(suite.T(), "推荐2", activeFeatured[1].Title)
}

// TestUpdateResourceStatus 测试更新资源状态
func (suite *VideoModelTestSuite) TestUpdateResourceStatus() {
	resource := suite.dbHelper.CreateTestResource(suite.T(), "测试视频", "beginner", 300)

	// 更新资源状态（假设有状态字段）
	// 这里需要根据实际的Resource模型字段来调整
	originalUpdatedAt := resource.UpdatedAt
	time.Sleep(time.Second) // 确保时间戳不同

	err := suite.videoModel.Update(resource, "id = ?", resource.Id)
	require.NoError(suite.T(), err)

	// 验证更新时间已改变
	var updatedResource model.Resource
	err = suite.helper.DB.First(&updatedResource, "id = ?", resource.Id).Error
	require.NoError(suite.T(), err)
	assert.True(suite.T(), updatedResource.UpdatedAt.After(originalUpdatedAt))
}

// 运行测试套件
func TestVideoModelSuite(t *testing.T) {
	suite.Run(t, new(VideoModelTestSuite))
}
