package video

import (
	"bytes"
	"encoding/json"
	"loop/internal/model"
	"loop/test/utils"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

// VideoApiTestSuite 视频API测试套件
type VideoApiTestSuite struct {
	suite.Suite
	helper       *utils.TestHelper
	dbHelper     *utils.DBHelper
	assertHelper *utils.AssertHelper
	router       *gin.Engine
}

// SetupSuite 设置测试套件
func (suite *VideoApiTestSuite) SetupSuite() {
	suite.helper = utils.NewTestHelper(suite.T())
	suite.dbHelper = utils.NewDBHelper(suite.helper.DB)
	suite.assertHelper = utils.NewAssertHelper(suite.T())

	// 设置路由
	suite.router = gin.New()
	suite.setupRoutes()
}

// setupRoutes 设置测试路由
func (suite *VideoApiTestSuite) setupRoutes() {
	v1 := suite.router.Group("/api/v1")

	// 公开路由
	v1.GET("/resources", suite.mockGetResources)
	v1.GET("/resources/:id", suite.mockGetResourceDetail)
	v1.GET("/series", suite.mockGetSeries)
	v1.GET("/series/:id/resources", suite.mockGetSeriesResources)
	v1.GET("/categories", suite.mockGetCategories)
	v1.GET("/featured", suite.mockGetFeaturedContents)

	// 需要认证的路由
	protected := v1.Group("/")
	protected.Use(suite.mockJWTMiddleware())
	{
		protected.POST("/resources/:id/download", suite.mockDownloadResource)
		protected.GET("/resources/local", suite.mockGetLocalResources)
		protected.DELETE("/resources/local/:id", suite.mockDeleteLocalResource)
		protected.POST("/resources/:id/remote", suite.mockCreateRemoteResourceRelation)
		protected.GET("/resources/remote", suite.mockGetRemoteResources)
	}
}

// mockJWTMiddleware 模拟JWT中间件
func (suite *VideoApiTestSuite) mockJWTMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		uid := c.GetHeader("X-Test-UID")
		if uid != "" {
			c.Set("uid", uid)
			c.Set("username", "testuser")
			c.Set("status", 0)
		}
		c.Next()
	}
}

// 模拟API处理函数
func (suite *VideoApiTestSuite) mockGetResources(c *gin.Context) {
	level := c.Query("level")
	category := c.Query("category")
	page := c.DefaultQuery("page", "1")
	limit := c.DefaultQuery("limit", "20")

	var resources []model.Resource
	query := suite.helper.DB.Model(&model.Resource{})

	if level != "" {
		query = query.Where("level = ?", level)
	}
	if category != "" {
		// 这里需要通过分类关系表查询，简化处理
		query = query.Where("id IN (SELECT resource_id FROM category_resource_relations WHERE category_id = ?)", category)
	}

	err := query.Find(&resources).Error
	if err != nil {
		c.JSON(500, gin.H{"code": 500, "msg": "查询失败"})
		return
	}

	c.JSON(200, gin.H{
		"code": 0,
		"msg":  "success",
		"data": gin.H{
			"list":  resources,
			"total": len(resources),
			"page":  page,
			"limit": limit,
		},
	})
}

func (suite *VideoApiTestSuite) mockGetResourceDetail(c *gin.Context) {
	resourceId := c.Param("id")

	var resource model.Resource
	err := suite.helper.DB.First(&resource, "id = ?", resourceId).Error
	if err != nil {
		c.JSON(404, gin.H{"code": 404, "msg": "资源不存在"})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success", "data": resource})
}

func (suite *VideoApiTestSuite) mockGetSeries(c *gin.Context) {
	var series []model.Series
	err := suite.helper.DB.Find(&series).Error
	if err != nil {
		c.JSON(500, gin.H{"code": 500, "msg": "查询失败"})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success", "data": series})
}

func (suite *VideoApiTestSuite) mockGetSeriesResources(c *gin.Context) {
	seriesId := c.Param("id")

	var relations []model.SeriesResourceRelation
	err := suite.helper.DB.Where("series_id = ?", seriesId).Order("sort_order ASC").Find(&relations).Error
	if err != nil {
		c.JSON(500, gin.H{"code": 500, "msg": "查询失败"})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success", "data": relations})
}

func (suite *VideoApiTestSuite) mockGetCategories(c *gin.Context) {
	var categories []model.Category
	err := suite.helper.DB.Find(&categories).Error
	if err != nil {
		c.JSON(500, gin.H{"code": 500, "msg": "查询失败"})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success", "data": categories})
}

func (suite *VideoApiTestSuite) mockGetFeaturedContents(c *gin.Context) {
	var featured []model.FeaturedContent
	err := suite.helper.DB.Where("status = ?", 1).Order("sort_order ASC").Find(&featured).Error
	if err != nil {
		c.JSON(500, gin.H{"code": 500, "msg": "查询失败"})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success", "data": featured})
}

func (suite *VideoApiTestSuite) mockDownloadResource(c *gin.Context) {
	uid := c.GetString("uid")
	resourceId := c.Param("id")

	if uid == "" {
		c.JSON(401, gin.H{"code": 401, "msg": "未授权"})
		return
	}

	// 检查资源是否存在
	var resource model.Resource
	err := suite.helper.DB.First(&resource, "id = ?", resourceId).Error
	if err != nil {
		c.JSON(404, gin.H{"code": 404, "msg": "资源不存在"})
		return
	}

	// 创建本地资源记录
	localResource := &model.UserLocalResource{
		Uid:        uid,
		ResourceId: resourceId,
		LocalPath:  "/local/path/" + resourceId + ".mp4",
		FileSize:   1024000,
		Status:     0, // 下载中
		Timestamp:  time.Now().UnixMilli(),
	}

	err = suite.helper.DB.Create(localResource).Error
	if err != nil {
		c.JSON(500, gin.H{"code": 500, "msg": "下载失败"})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success", "data": localResource})
}

func (suite *VideoApiTestSuite) mockGetLocalResources(c *gin.Context) {
	uid := c.GetString("uid")

	var localResources []model.UserLocalResource
	err := suite.helper.DB.Where("uid = ?", uid).Order("timestamp DESC").Find(&localResources).Error
	if err != nil {
		c.JSON(500, gin.H{"code": 500, "msg": "查询失败"})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success", "data": localResources})
}

func (suite *VideoApiTestSuite) mockDeleteLocalResource(c *gin.Context) {
	uid := c.GetString("uid")
	localResourceId := c.Param("id")

	err := suite.helper.DB.Where("id = ? AND uid = ?", localResourceId, uid).Delete(&model.UserLocalResource{}).Error
	if err != nil {
		c.JSON(500, gin.H{"code": 500, "msg": "删除失败"})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success"})
}

func (suite *VideoApiTestSuite) mockCreateRemoteResourceRelation(c *gin.Context) {
	uid := c.GetString("uid")
	resourceId := c.Param("id")

	var req struct {
		RemoteUrl string `json:"remote_url"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{"code": 400, "msg": "参数错误"})
		return
	}

	relation := &model.UserRemoteResourceRelation{
		Uid:        uid,
		ResourceId: resourceId,
		RemoteUrl:  req.RemoteUrl,
		Status:     1,
		Timestamp:  time.Now().UnixMilli(),
	}

	err := suite.helper.DB.Create(relation).Error
	if err != nil {
		c.JSON(500, gin.H{"code": 500, "msg": "创建失败"})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success", "data": relation})
}

func (suite *VideoApiTestSuite) mockGetRemoteResources(c *gin.Context) {
	uid := c.GetString("uid")

	var remoteResources []model.UserRemoteResourceRelation
	err := suite.helper.DB.Where("uid = ?", uid).Order("timestamp DESC").Find(&remoteResources).Error
	if err != nil {
		c.JSON(500, gin.H{"code": 500, "msg": "查询失败"})
		return
	}

	c.JSON(200, gin.H{"code": 0, "msg": "success", "data": remoteResources})
}

// TearDownSuite 清理测试套件
func (suite *VideoApiTestSuite) TearDownSuite() {
	suite.helper.CleanupDB(suite.T())
}

// SetupTest 每个测试前的设置
func (suite *VideoApiTestSuite) SetupTest() {
	// 清理测试数据
	tables := []string{
		"user_remote_resource_relations", "user_local_resources",
		"featured_contents", "series_resource_relations",
		"category_resource_relations", "resource_relations",
		"resources", "series_relations", "series",
		"categories", "category_types",
	}
	suite.dbHelper.CleanupTestData(suite.T(), tables...)
}

// TestGetResourcesAPI 测试获取资源列表API
func (suite *VideoApiTestSuite) TestGetResourcesAPI() {
	// 创建测试资源
	resource1 := suite.dbHelper.CreateTestResource(suite.T(), "初级视频1", "beginner", 300)
	resource2 := suite.dbHelper.CreateTestResource(suite.T(), "初级视频2", "beginner", 400)
	resource3 := suite.dbHelper.CreateTestResource(suite.T(), "中级视频1", "intermediate", 500)

	// 测试获取所有资源
	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/resources", nil)
	suite.router.ServeHTTP(w, req)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证返回的资源数据
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)

	data, ok := response["data"].(map[string]interface{})
	require.True(suite.T(), ok)
	
	list, ok := data["list"].([]interface{})
	require.True(suite.T(), ok)
	assert.Len(suite.T(), list, 3)

	// 测试按难度筛选
	w = httptest.NewRecorder()
	req = httptest.NewRequest("GET", "/api/v1/resources?level=beginner", nil)
	suite.router.ServeHTTP(w, req)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	err = json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)
	data = response["data"].(map[string]interface{})
	list = data["list"].([]interface{})
	assert.Len(suite.T(), list, 2) // 只有2个初级视频
}

// TestGetResourceDetailAPI 测试获取资源详情API
func (suite *VideoApiTestSuite) TestGetResourceDetailAPI() {
	resource := suite.dbHelper.CreateTestResource(suite.T(), "测试视频", "beginner", 300)

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/resources/"+resource.Id, nil)
	suite.router.ServeHTTP(w, req)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证返回的资源详情
	var response map[string]interface{}
	err := json.Unmarshal(w.Body.Bytes(), &response)
	require.NoError(suite.T(), err)

	data, ok := response["data"].(map[string]interface{})
	require.True(suite.T(), ok)
	assert.Equal(suite.T(), resource.Title, data["title"])
	assert.Equal(suite.T(), resource.Level, data["level"])
}

// TestGetSeriesAPI 测试获取系列列表API
func (suite *VideoApiTestSuite) TestGetSeriesAPI() {
	// 创建测试系列
	series1 := suite.dbHelper.CreateTestSeries(suite.T(), "系列1")
	series2 := suite.dbHelper.CreateTestSeries(suite.T(), "系列2")

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/series", nil)
	suite.router.ServeHTTP(w, req)

	suite.assertHelper.AssertSuccessResponse(w, nil)
	suite.assertHelper.AssertListResponse(w, 2)
}

// TestGetSeriesResourcesAPI 测试获取系列资源API
func (suite *VideoApiTestSuite) TestGetSeriesResourcesAPI() {
	// 创建系列和资源
	series := suite.dbHelper.CreateTestSeries(suite.T(), "测试系列")
	resource1 := suite.dbHelper.CreateTestResource(suite.T(), "视频1", "beginner", 300)
	resource2 := suite.dbHelper.CreateTestResource(suite.T(), "视频2", "beginner", 400)

	// 创建系列资源关系
	relations := []*model.SeriesResourceRelation{
		{SeriesId: series.Id, ResourceId: resource1.Id, SortOrder: 1},
		{SeriesId: series.Id, ResourceId: resource2.Id, SortOrder: 2},
	}

	for _, relation := range relations {
		err := suite.helper.DB.Create(relation).Error
		require.NoError(suite.T(), err)
	}

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/series/"+series.Id+"/resources", nil)
	suite.router.ServeHTTP(w, req)

	suite.assertHelper.AssertSuccessResponse(w, nil)
	suite.assertHelper.AssertListResponse(w, 2)
}

// TestDownloadResourceAPI 测试下载资源API
func (suite *VideoApiTestSuite) TestDownloadResourceAPI() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "videoapiuser001")
	resource := suite.dbHelper.CreateTestResource(suite.T(), "测试视频", "beginner", 300)

	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/resources/"+resource.Id+"/download", nil)
	req.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, req)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证本地资源记录已创建
	var localResource model.UserLocalResource
	err := suite.helper.DB.First(&localResource, "uid = ? AND resource_id = ?", testUser.Id, resource.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), testUser.Id, localResource.Uid)
	assert.Equal(suite.T(), resource.Id, localResource.ResourceId)
	assert.Equal(suite.T(), 0, localResource.Status) // 下载中
}

// TestGetLocalResourcesAPI 测试获取本地资源API
func (suite *VideoApiTestSuite) TestGetLocalResourcesAPI() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "videoapiuser002")
	resource := suite.dbHelper.CreateTestResource(suite.T(), "测试视频", "beginner", 300)

	// 创建本地资源记录
	localResource := &model.UserLocalResource{
		Uid:        testUser.Id,
		ResourceId: resource.Id,
		LocalPath:  "/local/path/video.mp4",
		FileSize:   1024000,
		Status:     1, // 下载完成
		Timestamp:  time.Now().UnixMilli(),
	}
	err := suite.helper.DB.Create(localResource).Error
	require.NoError(suite.T(), err)

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/resources/local", nil)
	req.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, req)

	suite.assertHelper.AssertSuccessResponse(w, nil)
	suite.assertHelper.AssertListResponse(w, 1)
}

// TestGetFeaturedContentsAPI 测试获取特色内容API
func (suite *VideoApiTestSuite) TestGetFeaturedContentsAPI() {
	// 创建特色内容
	resource := suite.dbHelper.CreateTestResource(suite.T(), "特色视频", "intermediate", 600)
	featured := &model.FeaturedContent{
		ResourceId:  resource.Id,
		Title:       "本周推荐",
		Description: "精选优质内容",
		SortOrder:   1,
		Status:      1, // 启用
	}
	err := suite.helper.DB.Create(featured).Error
	require.NoError(suite.T(), err)

	w := httptest.NewRecorder()
	req := httptest.NewRequest("GET", "/api/v1/featured", nil)
	suite.router.ServeHTTP(w, req)

	suite.assertHelper.AssertSuccessResponse(w, nil)
	suite.assertHelper.AssertListResponse(w, 1)
}

// TestCreateRemoteResourceRelationAPI 测试创建远程资源关系API
func (suite *VideoApiTestSuite) TestCreateRemoteResourceRelationAPI() {
	testUser := suite.dbHelper.CreateTestUser(suite.T(), "videoapiuser003")
	resource := suite.dbHelper.CreateTestResource(suite.T(), "测试视频", "beginner", 300)

	reqData := map[string]interface{}{
		"remote_url": "https://remote.com/video.mp4",
	}

	jsonData, _ := json.Marshal(reqData)
	w := httptest.NewRecorder()
	httpReq := httptest.NewRequest("POST", "/api/v1/resources/"+resource.Id+"/remote", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("X-Test-UID", testUser.Id)

	suite.router.ServeHTTP(w, httpReq)

	suite.assertHelper.AssertSuccessResponse(w, nil)

	// 验证远程资源关系已创建
	var relation model.UserRemoteResourceRelation
	err := suite.helper.DB.First(&relation, "uid = ? AND resource_id = ?", testUser.Id, resource.Id).Error
	require.NoError(suite.T(), err)
	assert.Equal(suite.T(), "https://remote.com/video.mp4", relation.RemoteUrl)
}

// TestUnauthorizedAccess 测试未授权访问
func (suite *VideoApiTestSuite) TestUnauthorizedAccess() {
	resource := suite.dbHelper.CreateTestResource(suite.T(), "测试视频", "beginner", 300)

	w := httptest.NewRecorder()
	req := httptest.NewRequest("POST", "/api/v1/resources/"+resource.Id+"/download", nil)
	// 不设置X-Test-UID头部

	suite.router.ServeHTTP(w, req)

	assert.Equal(suite.T(), 401, w.Code)
}

// 运行测试套件
func TestVideoApiSuite(t *testing.T) {
	suite.Run(t, new(VideoApiTestSuite))
}
