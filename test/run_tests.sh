#!/bin/bash

# 测试运行脚本
# 用于执行所有测试用例并生成测试报告

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT=$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)
TEST_DIR="$PROJECT_ROOT/test"
COVERAGE_DIR="$TEST_DIR/coverage"
REPORTS_DIR="$TEST_DIR/reports"

# 创建必要的目录
mkdir -p "$COVERAGE_DIR"
mkdir -p "$REPORTS_DIR"

echo -e "${BLUE}=== LSEnglish Backend Test Suite ===${NC}"
echo -e "${BLUE}Project Root: $PROJECT_ROOT${NC}"
echo -e "${BLUE}Test Directory: $TEST_DIR${NC}"
echo ""

# 检查Go环境
if ! command -v go &> /dev/null; then
    echo -e "${RED}Error: Go is not installed or not in PATH${NC}"
    exit 1
fi

echo -e "${GREEN}Go version:${NC}"
go version
echo ""

# 检查测试环境变量
if [ -z "$TEST_DB_DSN" ]; then
    echo -e "${YELLOW}Warning: TEST_DB_DSN not set, using default test database${NC}"
    export TEST_DB_DSN="root:password@tcp(localhost:3306)/lsenglish_test?charset=utf8mb4&parseTime=True&loc=Local"
fi

if [ -z "$TEST_REDIS_ADDR" ]; then
    echo -e "${YELLOW}Warning: TEST_REDIS_ADDR not set, using default Redis address${NC}"
    export TEST_REDIS_ADDR="localhost:6379"
fi

echo -e "${GREEN}Test Environment:${NC}"
echo "  DB DSN: $TEST_DB_DSN"
echo "  Redis: $TEST_REDIS_ADDR"
echo ""

# 函数：运行特定模块的测试
run_module_tests() {
    local module=$1
    local module_path="$TEST_DIR/$module"
    
    if [ ! -d "$module_path" ]; then
        echo -e "${YELLOW}Warning: Module $module not found, skipping...${NC}"
        return 0
    fi
    
    echo -e "${BLUE}Running $module module tests...${NC}"
    
    # 运行测试并生成覆盖率报告
    cd "$PROJECT_ROOT"
    go test -v -race -coverprofile="$COVERAGE_DIR/${module}_coverage.out" \
        -covermode=atomic \
        -timeout=30m \
        "./test/$module/..." 2>&1 | tee "$REPORTS_DIR/${module}_test.log"
    
    local exit_code=${PIPESTATUS[0]}
    
    if [ $exit_code -eq 0 ]; then
        echo -e "${GREEN}✓ $module module tests passed${NC}"
    else
        echo -e "${RED}✗ $module module tests failed${NC}"
        return $exit_code
    fi
    
    # 生成HTML覆盖率报告
    if [ -f "$COVERAGE_DIR/${module}_coverage.out" ]; then
        go tool cover -html="$COVERAGE_DIR/${module}_coverage.out" \
            -o "$REPORTS_DIR/${module}_coverage.html"
        echo -e "${GREEN}Coverage report generated: $REPORTS_DIR/${module}_coverage.html${NC}"
    fi
    
    echo ""
    return 0
}

# 函数：合并覆盖率报告
merge_coverage_reports() {
    echo -e "${BLUE}Merging coverage reports...${NC}"
    
    # 创建合并的覆盖率文件
    local merged_coverage="$COVERAGE_DIR/merged_coverage.out"
    
    # 写入mode行
    echo "mode: atomic" > "$merged_coverage"
    
    # 合并所有覆盖率文件
    for coverage_file in "$COVERAGE_DIR"/*_coverage.out; do
        if [ -f "$coverage_file" ]; then
            # 跳过mode行，只添加覆盖率数据
            tail -n +2 "$coverage_file" >> "$merged_coverage"
        fi
    done
    
    # 生成合并的HTML报告
    if [ -f "$merged_coverage" ]; then
        go tool cover -html="$merged_coverage" -o "$REPORTS_DIR/total_coverage.html"
        echo -e "${GREEN}Total coverage report generated: $REPORTS_DIR/total_coverage.html${NC}"
        
        # 显示总体覆盖率
        local total_coverage=$(go tool cover -func="$merged_coverage" | grep "total:" | awk '{print $3}')
        echo -e "${GREEN}Total Coverage: $total_coverage${NC}"
    fi
    
    echo ""
}

# 函数：生成测试报告摘要
generate_test_summary() {
    echo -e "${BLUE}Generating test summary...${NC}"
    
    local summary_file="$REPORTS_DIR/test_summary.txt"
    local html_summary="$REPORTS_DIR/test_summary.html"
    
    {
        echo "LSEnglish Backend Test Summary"
        echo "=============================="
        echo "Generated at: $(date)"
        echo ""
        
        echo "Test Results:"
        echo "-------------"
        
        local total_tests=0
        local passed_tests=0
        local failed_tests=0
        
        for log_file in "$REPORTS_DIR"/*_test.log; do
            if [ -f "$log_file" ]; then
                local module=$(basename "$log_file" "_test.log")
                local module_tests=$(grep -c "=== RUN" "$log_file" 2>/dev/null || echo "0")
                local module_passed=$(grep -c "--- PASS:" "$log_file" 2>/dev/null || echo "0")
                local module_failed=$(grep -c "--- FAIL:" "$log_file" 2>/dev/null || echo "0")
                
                echo "$module: $module_tests tests, $module_passed passed, $module_failed failed"
                
                total_tests=$((total_tests + module_tests))
                passed_tests=$((passed_tests + module_passed))
                failed_tests=$((failed_tests + module_failed))
            fi
        done
        
        echo ""
        echo "Total: $total_tests tests, $passed_tests passed, $failed_tests failed"
        echo ""
        
        # 覆盖率信息
        if [ -f "$COVERAGE_DIR/merged_coverage.out" ]; then
            echo "Coverage Information:"
            echo "--------------------"
            go tool cover -func="$COVERAGE_DIR/merged_coverage.out" | tail -1
            echo ""
        fi
        
        echo "Reports Location:"
        echo "----------------"
        echo "Summary: $summary_file"
        echo "HTML Summary: $html_summary"
        echo "Coverage Reports: $REPORTS_DIR/*_coverage.html"
        echo "Test Logs: $REPORTS_DIR/*_test.log"
        
    } | tee "$summary_file"
    
    # 生成HTML摘要（简单版本）
    {
        echo "<html><head><title>Test Summary</title></head><body>"
        echo "<h1>LSEnglish Backend Test Summary</h1>"
        echo "<pre>"
        cat "$summary_file"
        echo "</pre>"
        echo "</body></html>"
    } > "$html_summary"
    
    echo -e "${GREEN}Test summary generated: $summary_file${NC}"
    echo ""
}

# 主执行流程
main() {
    local start_time=$(date +%s)
    
    # 清理之前的报告
    echo -e "${BLUE}Cleaning previous reports...${NC}"
    rm -f "$COVERAGE_DIR"/*.out
    rm -f "$REPORTS_DIR"/*.log
    rm -f "$REPORTS_DIR"/*.html
    rm -f "$REPORTS_DIR"/*.txt
    echo ""
    
    # 定义要测试的模块
    local modules=("user" "vip" "benefit" "plan" "player" "video")
    local failed_modules=()
    
    # 运行各模块测试
    for module in "${modules[@]}"; do
        if ! run_module_tests "$module"; then
            failed_modules+=("$module")
        fi
    done
    
    # 合并覆盖率报告
    merge_coverage_reports
    
    # 生成测试摘要
    generate_test_summary
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo -e "${BLUE}=== Test Execution Complete ===${NC}"
    echo -e "${GREEN}Total execution time: ${duration}s${NC}"
    
    # 检查是否有失败的模块
    if [ ${#failed_modules[@]} -gt 0 ]; then
        echo -e "${RED}Failed modules: ${failed_modules[*]}${NC}"
        echo -e "${RED}Some tests failed. Please check the logs for details.${NC}"
        exit 1
    else
        echo -e "${GREEN}All tests passed successfully!${NC}"
        exit 0
    fi
}

# 处理命令行参数
case "${1:-all}" in
    "user"|"vip"|"benefit"|"plan"|"player"|"video")
        echo -e "${BLUE}Running tests for module: $1${NC}"
        run_module_tests "$1"
        ;;
    "all"|"")
        main
        ;;
    "clean")
        echo -e "${BLUE}Cleaning test reports...${NC}"
        rm -rf "$COVERAGE_DIR"
        rm -rf "$REPORTS_DIR"
        echo -e "${GREEN}Test reports cleaned${NC}"
        ;;
    "help"|"-h"|"--help")
        echo "Usage: $0 [module|all|clean|help]"
        echo ""
        echo "Commands:"
        echo "  all              Run all tests (default)"
        echo "  user             Run user module tests only"
        echo "  vip              Run VIP module tests only"
        echo "  benefit          Run benefit module tests only"
        echo "  plan             Run plan module tests only"
        echo "  player           Run player module tests only"
        echo "  video            Run video module tests only"
        echo "  clean            Clean test reports"
        echo "  help             Show this help message"
        echo ""
        echo "Environment Variables:"
        echo "  TEST_DB_DSN      Test database connection string"
        echo "  TEST_REDIS_ADDR  Test Redis server address"
        ;;
    *)
        echo -e "${RED}Unknown command: $1${NC}"
        echo "Use '$0 help' for usage information"
        exit 1
        ;;
esac
